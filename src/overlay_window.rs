use crate::entities::{Player, EspConfig, BoundingBox, Vector2f, Team};
use eframe::egui::{self, Color32, FontId, Pos2, Rect, Stroke, Vec2};
use eframe::{App, Frame};
use std::sync::{Arc, Mutex};

pub struct OverlayWindow {
    pub config: Arc<Mutex<EspConfig>>,
    pub players: Arc<Mutex<Vec<Player>>>,
    pub local_team: Arc<Mutex<Team>>,
    pub show_menu: bool,
    pub menu_key_pressed: bool,
    pub show_debug: bool,
}

impl OverlayWindow {
    pub fn new(
        config: Arc<Mutex<EspConfig>>,
        players: Arc<Mutex<Vec<Player>>>,
        local_team: Arc<Mutex<Team>>,
    ) -> Self {
        Self {
            config,
            players,
            local_team,
            show_menu: false,
            menu_key_pressed: false,
            show_debug: false,
        }
    }

    fn draw_esp_box(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox, config: &EspConfig) {
        if !config.draw_boxes {
            return;
        }

        let local_team = if let Ok(team) = self.local_team.try_lock() {
            *team
        } else {
            Team::None
        };

        if config.enemies_only && !player.is_enemy(local_team) {
            return;
        }

        let color = Color32::from_rgba_unmultiplied(
            (player.team.color()[0] * 255.0) as u8,
            (player.team.color()[1] * 255.0) as u8,
            (player.team.color()[2] * 255.0) as u8,
            200,
        );

        let stroke = Stroke::new(config.box_thickness, color);
        
        // Draw main box
        let rect = Rect::from_min_max(
            Pos2::new(bbox.top_left.x, bbox.top_left.y),
            Pos2::new(bbox.bottom_right.x, bbox.bottom_right.y),
        );
        
        ui.painter().rect_stroke(rect, 0.0, stroke);
        
        // Draw health bar if enabled
        if config.draw_health && player.health > 0 {
            self.draw_health_bar(ui, player, bbox);
        }
    }

    fn draw_health_bar(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox) {
        let health_percentage = player.health_percentage();
        let bar_width = 4.0;
        let bar_height = bbox.height;
        
        // Background bar
        let bg_rect = Rect::from_min_size(
            Pos2::new(bbox.top_left.x - bar_width - 2.0, bbox.top_left.y),
            Vec2::new(bar_width, bar_height),
        );
        ui.painter().rect_filled(bg_rect, 0.0, Color32::from_rgba_unmultiplied(0, 0, 0, 150));
        
        // Health bar
        let health_height = bar_height * health_percentage;
        let health_rect = Rect::from_min_size(
            Pos2::new(bbox.top_left.x - bar_width - 2.0, bbox.bottom_right.y - health_height),
            Vec2::new(bar_width, health_height),
        );
        
        let health_color = if health_percentage > 0.6 {
            Color32::GREEN
        } else if health_percentage > 0.3 {
            Color32::YELLOW
        } else {
            Color32::RED
        };
        
        ui.painter().rect_filled(health_rect, 0.0, health_color);
        
        // Health text
        let health_text = format!("{}", player.health);
        let text_pos = Pos2::new(
            bbox.top_left.x - bar_width - 15.0,
            bbox.bottom_right.y - health_height - 8.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::RIGHT_CENTER,
            health_text,
            FontId::monospace(10.0),
            Color32::WHITE,
        );
    }

    fn draw_player_name(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox, config: &EspConfig) {
        if !config.draw_names || player.name.is_empty() {
            return;
        }

        let text_pos = Pos2::new(
            bbox.top_left.x + bbox.width / 2.0,
            bbox.top_left.y - 15.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::CENTER_BOTTOM,
            &player.name,
            FontId::proportional(config.text_size),
            Color32::WHITE,
        );
    }

    fn draw_distance(&self, ui: &mut egui::Ui, _player: &Player, bbox: &BoundingBox, distance: f32, config: &EspConfig) {
        if !config.draw_distance {
            return;
        }

        let distance_text = format!("{:.0}m", distance);
        let text_pos = Pos2::new(
            bbox.top_left.x + bbox.width / 2.0,
            bbox.bottom_right.y + 5.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::CENTER_TOP,
            distance_text,
            FontId::monospace(10.0),
            Color32::LIGHT_GRAY,
        );
    }

    fn draw_config_menu(&mut self, ctx: &egui::Context) {
        egui::Window::new("CS2 ESP Configuration")
            .default_width(400.0)
            .show(ctx, |ui| {
                ui.heading("ESP Settings");
                ui.separator();

                // ESP Settings
                if let Ok(mut config) = self.config.try_lock() {
                    ui.checkbox(&mut config.enabled, "Enable ESP");
                    ui.separator();

                    ui.checkbox(&mut config.draw_boxes, "Draw Boxes");
                    ui.checkbox(&mut config.draw_health, "Draw Health Bars");
                    ui.checkbox(&mut config.draw_names, "Draw Player Names");
                    ui.checkbox(&mut config.draw_distance, "Draw Distance");
                    ui.checkbox(&mut config.enemies_only, "Enemies Only");

                    ui.separator();

                    ui.add(egui::Slider::new(&mut config.box_thickness, 1.0..=5.0).text("Box Thickness"));
                    ui.add(egui::Slider::new(&mut config.text_size, 8.0..=24.0).text("Text Size"));
                    ui.add(egui::Slider::new(&mut config.max_distance, 100.0..=2000.0).text("Max Distance"));

                    ui.separator();

                    // Status
                    let status_color = if config.enabled {
                        Color32::GREEN
                    } else {
                        Color32::RED
                    };

                    ui.colored_label(
                        status_color,
                        if config.enabled { "ESP: ENABLED" } else { "ESP: DISABLED" }
                    );
                }

                ui.separator();

                // Debug section
                ui.checkbox(&mut self.show_debug, "Show Debug Info");

                if self.show_debug {
                    ui.collapsing("Debug Information", |ui| {
                        // Player count
                        if let Ok(players) = self.players.try_lock() {
                            ui.label(format!("Players detected: {}", players.len()));

                            for (i, player) in players.iter().enumerate().take(5) {
                                ui.label(format!(
                                    "Player {}: {} HP, Team: {:?}",
                                    i + 1,
                                    player.health,
                                    player.team
                                ));
                            }
                        }

                        ui.separator();

                        // Local team
                        if let Ok(local_team) = self.local_team.try_lock() {
                            ui.label(format!("Local team: {:?}", *local_team));
                        }

                        ui.separator();

                        // Instructions
                        ui.label("Instructions:");
                        ui.label("• Press INSERT to toggle this menu");
                        ui.label("• Set window to 'Above' in KDE window rules");
                        ui.label("• Window should not take focus for input passthrough");
                    });
                }
            });
    }
}

impl App for OverlayWindow {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut Frame) {
        // Handle menu toggle (INSERT key)
        ctx.input(|i| {
            if i.key_pressed(egui::Key::Insert) && !self.menu_key_pressed {
                self.show_menu = !self.show_menu;
                self.menu_key_pressed = true;
                println!("Menu toggled: {}", self.show_menu);
            } else if !i.key_pressed(egui::Key::Insert) {
                self.menu_key_pressed = false;
            }
        });

        // Keep window always on top
        ctx.send_viewport_cmd(egui::ViewportCommand::WindowLevel(egui::WindowLevel::AlwaysOnTop));

        // Background - transparent normally, semi-transparent when menu is open
        let ui_frame = if self.show_menu {
            egui::Frame::none().fill(Color32::from_rgba_unmultiplied(0, 0, 0, 100))
        } else {
            egui::Frame::none().fill(Color32::TRANSPARENT)
        };
        
        egui::CentralPanel::default().frame(ui_frame).show(ctx, |ui| {
            // Get current config
            let config = if let Ok(config) = self.config.try_lock() {
                config.clone()
            } else {
                return;
            };

            if !config.enabled {
                return;
            }

            // Get current players
            let players = if let Ok(players) = self.players.try_lock() {
                players.clone()
            } else {
                return;
            };

            let local_team = if let Ok(team) = self.local_team.try_lock() {
                *team
            } else {
                Team::None
            };

            // Draw ESP for each player
            for player in &players {
                if !player.should_draw() {
                    continue;
                }

                if config.enemies_only && !player.is_enemy(local_team) {
                    continue;
                }

                // Calculate bounding box from screen position
                let bbox = BoundingBox::new(
                    Vector2f::new(player.screen_position.x - 30.0, player.screen_position.y - 60.0),
                    Vector2f::new(player.screen_position.x + 30.0, player.screen_position.y + 10.0),
                );

                self.draw_esp_box(ui, player, &bbox, &config);
                self.draw_player_name(ui, player, &bbox, &config);
                
                // Calculate distance (placeholder)
                let distance = 100.0; // This would be calculated from actual positions
                self.draw_distance(ui, player, &bbox, distance, &config);
            }
        });

        // Draw config menu if enabled
        if self.show_menu {
            self.draw_config_menu(ctx);
        }

        // Request repaint for smooth rendering
        ctx.request_repaint();
    }
}
