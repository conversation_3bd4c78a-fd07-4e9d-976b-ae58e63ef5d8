use crate::entities::{EspConfig, Team, Player};
use eframe::egui::{self, Color32};
use eframe::{App, Frame};
use std::sync::{Arc, Mutex};

pub struct ConfigWindow {
    pub config: Arc<Mutex<EspConfig>>,
    pub players: Arc<Mutex<Vec<Player>>>,
    pub local_team: Arc<Mutex<Team>>,
    pub show_debug: bool,
}

impl ConfigWindow {
    pub fn new(
        config: Arc<Mutex<EspConfig>>,
        players: Arc<Mutex<Vec<Player>>>,
        local_team: Arc<Mutex<Team>>,
    ) -> Self {
        Self {
            config,
            players,
            local_team,
            show_debug: false,
        }
    }
}

impl App for ConfigWindow {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut Frame) {
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("CS2 ESP Configuration");
            ui.separator();

            // ESP Settings
            if let Ok(mut config) = self.config.try_lock() {
                ui.checkbox(&mut config.enabled, "Enable ESP");
                ui.separator();

                ui.checkbox(&mut config.draw_boxes, "Draw Boxes");
                ui.checkbox(&mut config.draw_health, "Draw Health Bars");
                ui.checkbox(&mut config.draw_names, "Draw Player Names");
                ui.checkbox(&mut config.draw_distance, "Draw Distance");
                ui.checkbox(&mut config.enemies_only, "Enemies Only");

                ui.separator();

                ui.add(egui::Slider::new(&mut config.box_thickness, 1.0..=5.0).text("Box Thickness"));
                ui.add(egui::Slider::new(&mut config.text_size, 8.0..=24.0).text("Text Size"));
                ui.add(egui::Slider::new(&mut config.max_distance, 100.0..=2000.0).text("Max Distance"));

                ui.separator();

                // Save/Load buttons
                ui.horizontal(|ui| {
                    if ui.button("Save Config").clicked() {
                        // TODO: Implement config saving
                        println!("Config saved (not implemented yet)");
                    }
                    if ui.button("Load Config").clicked() {
                        // TODO: Implement config loading
                        println!("Config loaded (not implemented yet)");
                    }
                });
            }

            ui.separator();

            // Debug section
            ui.checkbox(&mut self.show_debug, "Show Debug Info");
            
            if self.show_debug {
                ui.collapsing("Debug Information", |ui| {
                    // Player count
                    if let Ok(players) = self.players.try_lock() {
                        ui.label(format!("Players detected: {}", players.len()));
                        
                        for (i, player) in players.iter().enumerate() {
                            ui.label(format!(
                                "Player {}: {} HP, Team: {:?}, Pos: ({:.1}, {:.1}, {:.1})",
                                i + 1,
                                player.health,
                                player.team,
                                player.position.x,
                                player.position.y,
                                player.position.z
                            ));
                        }
                    }

                    ui.separator();

                    // Local team
                    if let Ok(local_team) = self.local_team.try_lock() {
                        ui.label(format!("Local team: {:?}", *local_team));
                    }

                    ui.separator();

                    // Instructions
                    ui.label("Instructions:");
                    ui.label("• This window controls ESP settings");
                    ui.label("• The overlay window shows the ESP");
                    ui.label("• Set overlay window to 'Above' in KDE window rules");
                    ui.label("• Make overlay fullscreen for best results");
                });
            }

            ui.separator();

            // Status
            if let Ok(config) = self.config.try_lock() {
                let status_color = if config.enabled {
                    Color32::GREEN
                } else {
                    Color32::RED
                };
                
                ui.colored_label(
                    status_color,
                    if config.enabled { "ESP: ENABLED" } else { "ESP: DISABLED" }
                );
            }
        });

        // Request repaint for live updates
        ctx.request_repaint();
    }
}
