use anyhow::{anyhow, Result};
use std::fs;
use std::io::{Read, Seek, Seek<PERSON>rom};
use std::mem;

pub struct ProcessMemory {
    pub mem_file: fs::File,
    pub process_id: u32,
    pub base_address: usize,
}

impl ProcessMemory {
    pub fn new() -> Result<Self> {
        let process_id = Self::find_cs2_process()?;
        let mem_file = fs::OpenOptions::new()
            .read(true)
            .open(format!("/proc/{}/mem", process_id))?;
        let base_address = Self::get_module_base_address(process_id, "cs2")?;

        Ok(ProcessMemory {
            mem_file,
            process_id,
            base_address,
        })
    }

    fn find_cs2_process() -> Result<u32> {
        Self::find_process_by_name("cs2")
    }

    fn find_process_by_name(process_name: &str) -> Result<u32> {
        let proc_dir = fs::read_dir("/proc")?;

        for entry in proc_dir {
            let entry = entry?;
            let file_name = entry.file_name();
            let file_name_str = file_name.to_string_lossy();

            // Check if directory name is a number (PID)
            if let Ok(pid) = file_name_str.parse::<u32>() {
                // Read the command line to check process name
                let cmdline_path = format!("/proc/{}/cmdline", pid);
                if let Ok(cmdline) = fs::read_to_string(&cmdline_path) {
                    if cmdline.contains(process_name) {
                        return Ok(pid);
                    }
                }

                // Also check comm file
                let comm_path = format!("/proc/{}/comm", pid);
                if let Ok(comm) = fs::read_to_string(&comm_path) {
                    if comm.trim().contains(process_name) {
                        return Ok(pid);
                    }
                }
            }
        }

        Err(anyhow!("CS2 process not found"))
    }

    fn get_module_base_address(_process_id: u32, _module_name: &str) -> Result<usize> {
        // For educational purposes, we'll use a placeholder
        // In a real implementation, you'd parse /proc/PID/maps to find the module base
        Ok(0x555555554000) // Typical base address for executables on Linux
    }

    pub fn read<T: Copy>(&mut self, address: usize) -> Result<T> {
        let mut buffer = vec![0u8; mem::size_of::<T>()];

        self.mem_file.seek(SeekFrom::Start(address as u64))?;
        self.mem_file.read_exact(&mut buffer)?;

        Ok(unsafe { *(buffer.as_ptr() as *const T) })
    }

    pub fn read_bytes(&mut self, address: usize, size: usize) -> Result<Vec<u8>> {
        let mut buffer = vec![0u8; size];

        self.mem_file.seek(SeekFrom::Start(address as u64))?;
        self.mem_file.read_exact(&mut buffer)?;

        Ok(buffer)
    }

    pub fn read_string(&mut self, address: usize, max_length: usize) -> Result<String> {
        let bytes = self.read_bytes(address, max_length)?;
        let null_pos = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
        Ok(String::from_utf8_lossy(&bytes[..null_pos]).to_string())
    }
}
