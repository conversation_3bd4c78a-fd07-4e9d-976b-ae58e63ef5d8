use anyhow::{anyhow, Result};
use std::fs;
use std::io::{Read, Seek, Seek<PERSON>rom};
use std::mem;

pub struct ProcessMemory {
    pub mem_file: fs::File,
    pub process_id: u32,
    pub base_address: usize,
}

impl ProcessMemory {
    pub fn new() -> Result<Self> {
        let process_id = Self::find_cs2_process()?;
        let mem_file = fs::OpenOptions::new()
            .read(true)
            .open(format!("/proc/{}/mem", process_id))?;
        let base_address = Self::get_module_base_address(process_id, "cs2")?;

        Ok(ProcessMemory {
            mem_file,
            process_id,
            base_address,
        })
    }

    fn find_cs2_process() -> Result<u32> {
        Self::find_process_by_name("cs2")
    }

    fn find_process_by_name(process_name: &str) -> Result<u32> {
        let proc_dir = fs::read_dir("/proc")?;
        let mut found_processes = Vec::new();

        for entry in proc_dir {
            let entry = entry?;
            let file_name = entry.file_name();
            let file_name_str = file_name.to_string_lossy();

            // Check if directory name is a number (PID)
            if let Ok(pid) = file_name_str.parse::<u32>() {
                // Read the command line to check process name
                let cmdline_path = format!("/proc/{}/cmdline", pid);
                if let Ok(cmdline) = fs::read_to_string(&cmdline_path) {
                    // CS2 might be named differently, check for various patterns
                    if cmdline.contains("cs2") || cmdline.contains("Counter-Strike") || cmdline.contains("steam") {
                        found_processes.push((pid, cmdline.clone()));
                        if cmdline.contains("cs2") {
                            println!("Found CS2 process: PID {} - {}", pid, cmdline.replace('\0', " "));
                            return Ok(pid);
                        }
                    }
                }

                // Also check comm file
                let comm_path = format!("/proc/{}/comm", pid);
                if let Ok(comm) = fs::read_to_string(&comm_path) {
                    let comm_trimmed = comm.trim();
                    if comm_trimmed.contains(process_name) || comm_trimmed.contains("cs2") {
                        println!("Found CS2 process via comm: PID {} - {}", pid, comm_trimmed);
                        return Ok(pid);
                    }
                }
            }
        }

        // Debug: print all found processes that might be related
        if !found_processes.is_empty() {
            println!("Found {} potentially related processes:", found_processes.len());
            for (pid, cmdline) in &found_processes {
                println!("  PID {}: {}", pid, cmdline.replace('\0', " "));
            }
        }

        Err(anyhow!("CS2 process not found. Make sure CS2 is running."))
    }

    fn get_module_base_address(process_id: u32, _module_name: &str) -> Result<usize> {
        // Read /proc/PID/maps to find the actual base address
        let maps_path = format!("/proc/{}/maps", process_id);
        let maps_content = fs::read_to_string(&maps_path)?;

        for line in maps_content.lines() {
            // Look for executable regions that might contain our module
            if line.contains("r-xp") || line.contains("rwxp") {
                // Parse the address range
                if let Some(addr_range) = line.split_whitespace().next() {
                    if let Some(start_addr) = addr_range.split('-').next() {
                        if let Ok(base_addr) = usize::from_str_radix(start_addr, 16) {
                            // For CS2, we typically want the main executable region
                            // This is usually the first executable region
                            println!("Found executable region at: 0x{:x} - {}", base_addr, line);
                            return Ok(base_addr);
                        }
                    }
                }
            }
        }

        // Fallback to a common base address
        println!("Warning: Could not find module base address, using fallback");
        Ok(0x555555554000)
    }

    pub fn read<T: Copy>(&mut self, address: usize) -> Result<T> {
        let mut buffer = vec![0u8; mem::size_of::<T>()];

        self.mem_file.seek(SeekFrom::Start(address as u64))?;
        self.mem_file.read_exact(&mut buffer)?;

        Ok(unsafe { *(buffer.as_ptr() as *const T) })
    }

    pub fn read_bytes(&mut self, address: usize, size: usize) -> Result<Vec<u8>> {
        let mut buffer = vec![0u8; size];

        self.mem_file.seek(SeekFrom::Start(address as u64))?;
        self.mem_file.read_exact(&mut buffer)?;

        Ok(buffer)
    }

    pub fn read_string(&mut self, address: usize, max_length: usize) -> Result<String> {
        let bytes = self.read_bytes(address, max_length)?;
        let null_pos = bytes.iter().position(|&b| b == 0).unwrap_or(bytes.len());
        Ok(String::from_utf8_lossy(&bytes[..null_pos]).to_string())
    }
}
