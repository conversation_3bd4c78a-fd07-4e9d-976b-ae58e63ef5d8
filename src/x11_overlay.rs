use crate::entities::{Player, EspConfig, Team};
use anyhow::Result;
use std::ffi::CString;
use std::ptr;
use std::sync::{Arc, Mutex};
use x11::xlib::*;

pub struct X11Overlay {
    display: *mut Display,
    window: Window,
    screen: i32,
    width: u32,
    height: u32,
    config: Arc<Mutex<EspConfig>>,
    players: Arc<Mutex<Vec<Player>>>,
    local_team: Arc<Mutex<Team>>,
    show_menu: bool,
}

impl X11Overlay {
    pub fn new(
        config: Arc<Mutex<EspConfig>>,
        players: Arc<Mutex<Vec<Player>>>,
        local_team: Arc<Mutex<Team>>,
    ) -> Result<Self> {
        unsafe {
            // Open display
            let display = XOpenDisplay(ptr::null());
            if display.is_null() {
                return Err(anyhow::anyhow!("Cannot open X11 display"));
            }

            let screen = XDefaultScreen(display);
            let root = XRootWindow(display, screen);
            
            // Get screen dimensions
            let width = XDisplayWidth(display, screen) as u32;
            let height = XDisplayHeight(display, screen) as u32;
            
            println!("X11 Screen resolution: {}x{}", width, height);

            // Create window attributes for transparency
            let mut attributes = XSetWindowAttributes {
                override_redirect: True,
                background_pixel: 0,
                border_pixel: 0,
                colormap: XDefaultColormap(display, screen),
                event_mask: KeyPressMask | KeyReleaseMask | ExposureMask,
                ..std::mem::zeroed()
            };

            // Create the overlay window
            let window = XCreateWindow(
                display,
                root,
                0, 0,
                width, height,
                0,
                XDefaultDepth(display, screen),
                InputOutput as u32,
                XDefaultVisual(display, screen),
                CWOverrideRedirect | CWBackPixel | CWBorderPixel | CWColormap | CWEventMask,
                &mut attributes,
            );

            if window == 0 {
                XCloseDisplay(display);
                return Err(anyhow::anyhow!("Cannot create X11 window"));
            }

            // Set window properties for overlay behavior
            Self::set_window_properties(display, window)?;

            // Map the window
            XMapWindow(display, window);
            XFlush(display);

            println!("X11 overlay window created successfully");
            println!("Window should be transparent and always on top");
            println!("Press INSERT to toggle menu, ESC to exit");

            Ok(Self {
                display,
                window,
                screen,
                width,
                height,
                config,
                players,
                local_team,
                show_menu: false,
            })
        }
    }

    unsafe fn set_window_properties(display: *mut Display, window: Window) -> Result<()> {
        // Set window type to notification/OSD
        let wm_window_type = XInternAtom(display, CString::new("_NET_WM_WINDOW_TYPE")?.as_ptr(), False);
        let wm_window_type_notification = XInternAtom(display, CString::new("_NET_WM_WINDOW_TYPE_NOTIFICATION")?.as_ptr(), False);
        
        XChangeProperty(
            display,
            window,
            wm_window_type,
            XA_ATOM,
            32,
            PropModeReplace,
            &wm_window_type_notification as *const _ as *const u8,
            1,
        );

        // Set window to be above all others
        let wm_state = XInternAtom(display, CString::new("_NET_WM_STATE")?.as_ptr(), False);
        let wm_state_above = XInternAtom(display, CString::new("_NET_WM_STATE_ABOVE")?.as_ptr(), False);
        let wm_state_skip_taskbar = XInternAtom(display, CString::new("_NET_WM_STATE_SKIP_TASKBAR")?.as_ptr(), False);
        
        let states = [wm_state_above, wm_state_skip_taskbar];
        XChangeProperty(
            display,
            window,
            wm_state,
            XA_ATOM,
            32,
            PropModeReplace,
            states.as_ptr() as *const u8,
            2,
        );

        // Set input shape to allow passthrough (this is the key for input passthrough)
        let shape_input = 2; // ShapeInput
        let shape_set = 0;   // ShapeSet
        
        // This requires the SHAPE extension - we'll try to set it
        // XShapeSelectInput(display, window, ShapeNotifyMask);
        // XShapeCombineRectangles(display, window, shape_input, 0, 0, ptr::null(), 0, shape_set, YXBanded);

        XFlush(display);
        Ok(())
    }

    pub fn run(&mut self) -> Result<()> {
        unsafe {
            let mut event: XEvent = std::mem::zeroed();
            
            loop {
                // Check for events (non-blocking)
                while XPending(self.display) > 0 {
                    XNextEvent(self.display, &mut event);
                    
                    match event.get_type() {
                        KeyPress => {
                            let key_event = XKeyEvent::from(event);
                            let keysym = XLookupKeysym(&mut key_event.into(), 0);
                            
                            match keysym as u32 {
                                0xff63 => { // Insert key
                                    self.show_menu = !self.show_menu;
                                    println!("Menu toggled: {}", self.show_menu);
                                }
                                0xff1b => { // Escape key
                                    println!("Exiting X11 overlay");
                                    return Ok(());
                                }
                                _ => {}
                            }
                        }
                        Expose => {
                            self.draw()?;
                        }
                        _ => {}
                    }
                }

                // Draw frame
                self.draw()?;
                
                // Small delay
                std::thread::sleep(std::time::Duration::from_millis(16)); // ~60 FPS
            }
        }
    }

    unsafe fn draw(&mut self) -> Result<()> {
        // Clear window (transparent)
        XClearWindow(self.display, self.window);

        // Get graphics context
        let gc = XDefaultGC(self.display, self.screen);

        // Draw ESP
        self.draw_esp(gc)?;

        // Draw menu if enabled
        if self.show_menu {
            self.draw_menu(gc)?;
        }

        XFlush(self.display);
        Ok(())
    }

    unsafe fn draw_esp(&mut self, gc: GC) -> Result<()> {
        let config = if let Ok(config) = self.config.try_lock() {
            config.clone()
        } else {
            return Ok(());
        };

        if !config.enabled {
            return Ok(());
        }

        let players = if let Ok(players) = self.players.try_lock() {
            players.clone()
        } else {
            return Ok(());
        };

        let local_team = if let Ok(team) = self.local_team.try_lock() {
            *team
        } else {
            Team::None
        };

        // Set drawing color (white for now)
        XSetForeground(self.display, gc, 0xFFFFFF);

        // Draw each player
        for player in &players {
            if !player.should_draw() {
                continue;
            }

            if config.enemies_only && !player.is_enemy(local_team) {
                continue;
            }

            self.draw_player_box(gc, player, &config)?;
        }

        Ok(())
    }

    unsafe fn draw_player_box(&mut self, gc: GC, player: &Player, config: &EspConfig) -> Result<()> {
        if !config.draw_boxes {
            return Ok(());
        }

        // Calculate box dimensions
        let box_width = 60;
        let box_height = 80;
        let x = player.screen_position.x as i32 - box_width / 2;
        let y = player.screen_position.y as i32 - box_height;

        // Set color based on team
        let color = match player.team {
            Team::Terrorist => 0xFF6600, // Orange
            Team::CounterTerrorist => 0x0066FF, // Blue
            _ => 0xFFFFFF, // White
        };

        XSetForeground(self.display, gc, color);

        // Draw box outline
        for i in 0..config.box_thickness as i32 {
            XDrawRectangle(
                self.display,
                self.window,
                gc,
                x - i,
                y - i,
                (box_width + 2 * i) as u32,
                (box_height + 2 * i) as u32,
            );
        }

        // Draw health bar
        if config.draw_health {
            self.draw_health_bar(gc, player, x - 8, y, box_height)?;
        }

        Ok(())
    }

    unsafe fn draw_health_bar(&mut self, gc: GC, player: &Player, x: i32, y: i32, height: i32) -> Result<()> {
        let bar_width = 4;
        let health_percentage = player.health_percentage();

        // Background (black)
        XSetForeground(self.display, gc, 0x000000);
        XFillRectangle(self.display, self.window, gc, x, y, bar_width as u32, height as u32);

        // Health bar
        let health_height = (height as f32 * health_percentage) as i32;
        let health_color = if health_percentage > 0.6 {
            0x00FF00 // Green
        } else if health_percentage > 0.3 {
            0xFFFF00 // Yellow
        } else {
            0xFF0000 // Red
        };

        XSetForeground(self.display, gc, health_color);
        XFillRectangle(
            self.display,
            self.window,
            gc,
            x,
            y + height - health_height,
            bar_width as u32,
            health_height as u32,
        );

        Ok(())
    }

    unsafe fn draw_menu(&mut self, gc: GC) -> Result<()> {
        // Draw semi-transparent background (simplified)
        XSetForeground(self.display, gc, 0x333333);
        XFillRectangle(self.display, self.window, gc, 50, 50, 400, 300);

        // Draw border
        XSetForeground(self.display, gc, 0xFFFFFF);
        XDrawRectangle(self.display, self.window, gc, 50, 50, 400, 300);

        // TODO: Add text rendering for menu items
        // For now, just show a placeholder rectangle
        XFillRectangle(self.display, self.window, gc, 60, 60, 200, 20);

        Ok(())
    }
}

impl Drop for X11Overlay {
    fn drop(&mut self) {
        unsafe {
            if !self.display.is_null() {
                XDestroyWindow(self.display, self.window);
                XCloseDisplay(self.display);
            }
        }
    }
}
