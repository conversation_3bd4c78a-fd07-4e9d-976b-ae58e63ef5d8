use crate::entities::{Player, EspConfig, Team};
use anyhow::Result;
use sdl2::event::Event;
use sdl2::keyboard::Keycode;
use sdl2::pixels::Color;
use sdl2::rect::Rect;
use sdl2::render::Canvas;
use sdl2::video::Window;
use sdl2::{EventPump, Sdl, VideoSubsystem};
use std::sync::{Arc, Mutex};
use std::time::Duration;

pub struct SdlOverlay {
    sdl_context: Sdl,
    video_subsystem: VideoSubsystem,
    window: Window,
    canvas: Canvas<Window>,
    event_pump: EventPump,
    config: Arc<Mutex<EspConfig>>,
    players: Arc<Mutex<Vec<Player>>>,
    local_team: Arc<Mutex<Team>>,
    show_menu: bool,
}

impl SdlOverlay {
    pub fn new(
        config: Arc<Mutex<EspConfig>>,
        players: Arc<Mutex<Vec<Player>>>,
        local_team: Arc<Mutex<Team>>,
    ) -> Result<Self> {
        let sdl_context = sdl2::init().map_err(|e| anyhow::anyhow!("SDL init failed: {}", e))?;
        let video_subsystem = sdl_context
            .video()
            .map_err(|e| anyhow::anyhow!("Video subsystem init failed: {}", e))?;

        // Get screen dimensions
        let display_mode = video_subsystem
            .desktop_display_mode(0)
            .map_err(|e| anyhow::anyhow!("Failed to get display mode: {}", e))?;

        println!("Screen resolution: {}x{}", display_mode.w, display_mode.h);

        // Create overlay window with specific flags for input passthrough
        let mut window_builder = video_subsystem
            .window("CS2 ESP Overlay", display_mode.w as u32, display_mode.h as u32);

        let window = window_builder
            .position(0, 0)
            .borderless()
            .always_on_top()
            .allow_highdpi()
            .build()
            .map_err(|e| anyhow::anyhow!("Window creation failed: {}", e))?;

        // Note: SDL2 transparency depends on the compositor
        println!("SDL2 overlay window created.");
        println!("For transparency: Enable compositor in KDE System Settings > Display > Compositor");
        println!("For input passthrough: Set window rules in KDE for 'CS2 ESP Overlay':");
        println!("  - Window type: On-screen display");
        println!("  - Accept focus: Force No");
        println!("  - Above: Force Yes");

        let mut canvas = window
            .into_canvas()
            .accelerated()
            .present_vsync()
            .build()
            .map_err(|e| anyhow::anyhow!("Canvas creation failed: {}", e))?;

        // Set blend mode for transparency
        canvas.set_blend_mode(sdl2::render::BlendMode::Blend);

        // Set the draw color to transparent for clearing
        canvas.set_draw_color(Color::RGBA(0, 0, 0, 0));

        let event_pump = sdl_context
            .event_pump()
            .map_err(|e| anyhow::anyhow!("Event pump creation failed: {}", e))?;

        Ok(Self {
            sdl_context,
            video_subsystem,
            window: canvas.window().clone(),
            canvas,
            event_pump,
            config,
            players,
            local_team,
            show_menu: false,
        })
    }

    pub fn run(&mut self) -> Result<()> {
        println!("SDL2 overlay started. Press INSERT to toggle menu, ESC to exit.");

        'running: loop {
            // Handle events
            for event in self.event_pump.poll_iter() {
                match event {
                    Event::Quit { .. } => break 'running,
                    Event::KeyDown {
                        keycode: Some(Keycode::Escape),
                        ..
                    } => break 'running,
                    Event::KeyDown {
                        keycode: Some(Keycode::Insert),
                        ..
                    } => {
                        self.show_menu = !self.show_menu;
                        println!("Menu toggled: {}", self.show_menu);
                    }
                    _ => {}
                }
            }

            // Clear screen with transparent background
            self.canvas.set_draw_color(Color::RGBA(0, 0, 0, 0));
            self.canvas.clear();

            // Draw ESP
            self.draw_esp()?;

            // Draw menu if enabled
            if self.show_menu {
                self.draw_menu()?;
            }

            // Present the frame
            self.canvas.present();

            // Small delay to prevent excessive CPU usage
            std::thread::sleep(Duration::from_millis(16)); // ~60 FPS
        }

        Ok(())
    }

    fn draw_esp(&mut self) -> Result<()> {
        let config = if let Ok(config) = self.config.try_lock() {
            config.clone()
        } else {
            return Ok(());
        };

        if !config.enabled {
            return Ok(());
        }

        let players = if let Ok(players) = self.players.try_lock() {
            players.clone()
        } else {
            return Ok(());
        };

        let local_team = if let Ok(team) = self.local_team.try_lock() {
            *team
        } else {
            Team::None
        };

        // Draw each player
        for player in &players {
            if !player.should_draw() {
                continue;
            }

            if config.enemies_only && !player.is_enemy(local_team) {
                continue;
            }

            self.draw_player_box(player, &config)?;
        }

        Ok(())
    }

    fn draw_player_box(&mut self, player: &Player, config: &EspConfig) -> Result<()> {
        if !config.draw_boxes {
            return Ok(());
        }

        // Calculate box dimensions
        let box_width = 60;
        let box_height = 80;
        let x = player.screen_position.x as i32 - box_width / 2;
        let y = player.screen_position.y as i32 - box_height;

        // Set color based on team
        let color = match player.team {
            Team::Terrorist => Color::RGB(255, 165, 0), // Orange
            Team::CounterTerrorist => Color::RGB(0, 100, 255), // Blue
            _ => Color::RGB(255, 255, 255), // White
        };

        self.canvas.set_draw_color(color);

        // Draw box outline
        for i in 0..config.box_thickness as i32 {
            let thick_rect = Rect::new(x - i, y - i, (box_width + 2 * i) as u32, (box_height + 2 * i) as u32);
            self.canvas.draw_rect(thick_rect)
                .map_err(|e| anyhow::anyhow!("Failed to draw rect: {}", e))?;
        }

        // Draw health bar
        if config.draw_health {
            self.draw_health_bar(player, x - 8, y, box_height)?;
        }

        Ok(())
    }

    fn draw_health_bar(&mut self, player: &Player, x: i32, y: i32, height: i32) -> Result<()> {
        let bar_width = 4;
        let health_percentage = player.health_percentage();

        // Background
        self.canvas.set_draw_color(Color::RGBA(0, 0, 0, 150));
        let bg_rect = Rect::new(x, y, bar_width as u32, height as u32);
        self.canvas.fill_rect(bg_rect)
            .map_err(|e| anyhow::anyhow!("Failed to fill rect: {}", e))?;

        // Health bar
        let health_height = (height as f32 * health_percentage) as i32;
        let health_color = if health_percentage > 0.6 {
            Color::RGB(0, 255, 0) // Green
        } else if health_percentage > 0.3 {
            Color::RGB(255, 255, 0) // Yellow
        } else {
            Color::RGB(255, 0, 0) // Red
        };

        self.canvas.set_draw_color(health_color);
        let health_rect = Rect::new(x, y + height - health_height, bar_width as u32, health_height as u32);
        self.canvas.fill_rect(health_rect)
            .map_err(|e| anyhow::anyhow!("Failed to fill health rect: {}", e))?;

        Ok(())
    }

    fn draw_menu(&mut self) -> Result<()> {
        // Draw semi-transparent background
        self.canvas.set_draw_color(Color::RGBA(0, 0, 0, 180));
        let menu_rect = Rect::new(50, 50, 400, 300);
        self.canvas.fill_rect(menu_rect)
            .map_err(|e| anyhow::anyhow!("Failed to fill menu rect: {}", e))?;

        // Draw border
        self.canvas.set_draw_color(Color::RGB(100, 100, 100));
        self.canvas.draw_rect(menu_rect)
            .map_err(|e| anyhow::anyhow!("Failed to draw menu border: {}", e))?;

        // TODO: Add text rendering for menu items
        // For now, just show a placeholder
        self.canvas.set_draw_color(Color::RGB(255, 255, 255));
        let title_rect = Rect::new(60, 60, 200, 20);
        self.canvas.fill_rect(title_rect)
            .map_err(|e| anyhow::anyhow!("Failed to fill title rect: {}", e))?;

        Ok(())
    }
}
