use crate::entities::{Player, LocalPlayer, Team, Vector3f};
use crate::math::{WorldToScreen, matrix_from_bytes};
use crate::memory::ProcessMemory;
use crate::offsets::cs2_dumper::{offsets, entity};
use anyhow::Result;
use nalgebra::Matrix4;
use std::time::{Duration, Instant};

pub struct EspEngine {
    memory: ProcessMemory,
    world_to_screen: WorldToScreen,
    local_player: LocalPlayer,
    players: Vec<Player>,
    last_update: Instant,
    update_interval: Duration,
}

impl EspEngine {
    pub fn new() -> Result<Self> {
        let memory = ProcessMemory::new()?;
        let world_to_screen = WorldToScreen::new(Matrix4::identity(), 1920.0, 1080.0);
        
        Ok(Self {
            memory,
            world_to_screen,
            local_player: LocalPlayer::new(),
            players: Vec::new(),
            last_update: Instant::now(),
            update_interval: Duration::from_millis(16), // ~60 FPS
        })
    }

    pub fn update(&mut self) -> Result<()> {
        if self.last_update.elapsed() < self.update_interval {
            return Ok(());
        }

        self.update_view_matrix()?;
        self.update_local_player()?;
        self.update_players()?;
        
        self.last_update = Instant::now();
        Ok(())
    }

    fn update_view_matrix(&mut self) -> Result<()> {
        // Read view matrix from CS2
        let client_base = self.memory.base_address;
        let view_render_ptr = self.memory.read::<usize>(
            client_base + offsets::libclient_so::dwViewRender
        )?;
        
        if view_render_ptr == 0 {
            return Ok(());
        }

        // View matrix is typically at offset 0x40 from ViewRender
        let view_matrix_address = view_render_ptr + 0x40;
        let matrix_bytes = self.memory.read_bytes(view_matrix_address, 64)?;
        
        if let Some(view_matrix) = matrix_from_bytes(&matrix_bytes) {
            // Update screen dimensions (you might want to read these from the game)
            let screen_width = 1920.0; // Could read from dwWindowWidth
            let screen_height = 1080.0; // Could read from dwWindowHeight
            
            self.world_to_screen.update(view_matrix, screen_width, screen_height);
            self.local_player.view_matrix = view_matrix;
            self.local_player.screen_width = screen_width;
            self.local_player.screen_height = screen_height;
        }

        Ok(())
    }

    fn update_local_player(&mut self) -> Result<()> {
        let client_base = self.memory.base_address;
        
        // Read local player controller
        let local_controller_ptr = self.memory.read::<usize>(
            client_base + offsets::libclient_so::dwLocalPlayerController
        )?;
        
        if local_controller_ptr == 0 {
            return Ok(());
        }

        // Read local player pawn
        let local_pawn_handle = self.memory.read::<u32>(
            local_controller_ptr + entity::m_hPlayerPawn
        )?;
        
        if local_pawn_handle == 0 {
            return Ok(());
        }

        // Get pawn address from entity list
        let entity_list_ptr = self.memory.read::<usize>(
            client_base + offsets::libclient_so::dwEntityList
        )?;
        
        let pawn_address = self.get_entity_address(entity_list_ptr, local_pawn_handle)?;
        
        if pawn_address == 0 {
            return Ok(());
        }

        // Read local player data
        self.local_player.player.pawn_address = pawn_address;
        self.local_player.player.controller_address = local_controller_ptr;
        
        // Read position
        let scene_node_ptr = self.memory.read::<usize>(pawn_address + entity::m_pGameSceneNode)?;
        if scene_node_ptr != 0 {
            let position: [f32; 3] = self.memory.read(scene_node_ptr + entity::m_vecOrigin)?;
            self.local_player.player.position = Vector3f::from(position);
        }

        // Read health and team
        self.local_player.player.health = self.memory.read::<i32>(pawn_address + entity::m_iHealth)?;
        self.local_player.player.team = Team::from(self.memory.read::<u8>(pawn_address + entity::m_iTeamNum)?);
        self.local_player.player.is_alive = self.local_player.player.health > 0;
        self.local_player.player.is_valid = true;

        Ok(())
    }

    fn update_players(&mut self) -> Result<()> {
        self.players.clear();
        
        let client_base = self.memory.base_address;
        let entity_list_ptr = self.memory.read::<usize>(
            client_base + offsets::libclient_so::dwEntityList
        )?;

        // Iterate through player controllers (typically indices 1-64)
        for i in 1..=64 {
            if let Ok(player) = self.read_player(entity_list_ptr, i) {
                if player.is_valid && player.entity_index != self.local_player.player.entity_index {
                    self.players.push(player);
                }
            }
        }

        // Update screen positions for all players
        for player in &mut self.players {
            if let Some(screen_pos) = self.world_to_screen.world_to_screen(player.position) {
                player.screen_position = screen_pos;
            }
        }

        Ok(())
    }

    fn read_player(&mut self, entity_list_ptr: usize, index: i32) -> Result<Player> {
        let mut player = Player::new();
        player.entity_index = index;

        // Get controller address
        let controller_address = self.get_entity_address(entity_list_ptr, index as u32)?;
        if controller_address == 0 {
            return Ok(player);
        }

        player.controller_address = controller_address;

        // Read pawn handle
        let pawn_handle = self.memory.read::<u32>(controller_address + entity::m_hPlayerPawn)?;
        if pawn_handle == 0 {
            return Ok(player);
        }

        // Get pawn address
        let pawn_address = self.get_entity_address(entity_list_ptr, pawn_handle)?;
        if pawn_address == 0 {
            return Ok(player);
        }

        player.pawn_address = pawn_address;

        // Read basic player data
        player.health = self.memory.read::<i32>(pawn_address + entity::m_iHealth)?;
        player.team = Team::from(self.memory.read::<u8>(pawn_address + entity::m_iTeamNum)?);
        player.is_alive = player.health > 0;

        // Read position from scene node
        let scene_node_ptr = self.memory.read::<usize>(pawn_address + entity::m_pGameSceneNode)?;
        if scene_node_ptr != 0 {
            let position: [f32; 3] = self.memory.read(scene_node_ptr + entity::m_vecOrigin)?;
            player.position = Vector3f::from(position);
        }

        // Read player name (simplified - actual implementation would be more complex)
        player.name = format!("Player{}", index);

        player.is_valid = true;
        Ok(player)
    }

    fn get_entity_address(&mut self, entity_list_ptr: usize, handle: u32) -> Result<usize> {
        let index = handle & 0x7FFF;
        let entry_ptr = self.memory.read::<usize>(entity_list_ptr + 0x8 * ((index >> 9) & 0x3F) as usize)?;

        if entry_ptr == 0 {
            return Ok(0);
        }

        let entity_ptr = self.memory.read::<usize>(entry_ptr + 0x78 * (index & 0x1FF) as usize)?;
        Ok(entity_ptr)
    }

    pub fn get_players(&self) -> &Vec<Player> {
        &self.players
    }

    pub fn get_local_team(&self) -> Team {
        self.local_player.player.team
    }

    pub fn is_ready(&self) -> bool {
        self.local_player.player.is_valid
    }
}
