mod entities;
mod esp;
mod esp_overlay;
mod math;
mod memory;
mod offsets;

use anyhow::Result;
use eframe::egui;
use esp::EspEngine;
use esp_overlay::EspOverlay;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

fn main() -> Result<()> {
    println!("CS2 External ESP - Educational Purpose Only");
    println!("Make sure CS2 is running with -insecure flag!");
    println!("Press INSERT to toggle menu");

    // Safety check - ensure this is for educational purposes
    println!("\n⚠️  IMPORTANT SAFETY NOTICE ⚠️");
    println!("This tool is for educational purposes only!");
    println!("Only use in private games with bots and -insecure flag!");
    println!("Do NOT use in public matchmaking!");

    // Wait for user confirmation
    println!("\nOptions:");
    println!("1. Press Enter to try connecting to CS2");
    println!("2. Type 'demo' and press Enter for demo mode (shows ESP without CS2)");
    print!("Choice: ");

    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;
    let demo_mode = input.trim().eq_ignore_ascii_case("demo");

    // Initialize ESP engine or demo mode
    let esp_engine = if demo_mode {
        println!("🎮 Starting in demo mode - ESP will show fake players");
        // In demo mode, we'll create fake players in the overlay
        None
    } else {
        match EspEngine::new() {
            Ok(engine) => {
                println!("✅ Successfully attached to CS2 process");
                Some(Arc::new(Mutex::new(engine)))
            }
            Err(e) => {
                eprintln!("❌ Failed to attach to CS2: {}", e);
                eprintln!("Make sure CS2 is running and you have the necessary permissions");
                eprintln!("Tip: Try running with 'demo' for demo mode");
                return Err(e);
            }
        }
    };

    // Create overlay
    let overlay = Arc::new(Mutex::new(EspOverlay::new()));

    // Start ESP update thread
    let overlay_clone = Arc::clone(&overlay);

    if let Some(esp_engine) = esp_engine {
        let esp_engine_clone = Arc::clone(&esp_engine);
        thread::spawn(move || {
            let mut error_count = 0;
            let mut last_error_print = std::time::Instant::now();

            loop {
                if let Ok(mut engine) = esp_engine_clone.try_lock() {
                    if let Err(e) = engine.update() {
                        error_count += 1;

                        // Only print errors every 5 seconds to avoid spam
                        if last_error_print.elapsed() > Duration::from_secs(5) {
                            eprintln!("ESP update error ({}): {}", error_count, e);
                            eprintln!("Make sure CS2 is running and you have proper permissions");
                            last_error_print = std::time::Instant::now();
                            error_count = 0;
                        }

                        thread::sleep(Duration::from_millis(1000)); // Wait longer on error
                        continue;
                    }

                    // Reset error count on successful update
                    error_count = 0;

                    if engine.is_ready() {
                        let players = engine.get_players().clone();
                        let local_team = engine.get_local_team();

                        if let Ok(mut overlay) = overlay_clone.try_lock() {
                            overlay.update_players(players, local_team);
                        }
                    }
                }

                thread::sleep(Duration::from_millis(16)); // ~60 FPS
            }
        });
    } else {
        // Demo mode - create fake players
        thread::spawn(move || {
            use crate::entities::{Player, Team, Vector2f, Vector3f};

            loop {
                let mut fake_players = Vec::new();

                // Create some fake players for demo
                for i in 0..6 {
                    let mut player = Player::new();
                    player.entity_index = i;
                    player.health = 100 - (i * 15);
                    player.team = if i % 2 == 0 { Team::Terrorist } else { Team::CounterTerrorist };
                    player.is_alive = true;
                    player.is_valid = true;
                    player.name = format!("DemoPlayer{}", i + 1);

                    // Set screen positions in a grid
                    let x = 200.0 + (i as f32 % 3.0) * 200.0;
                    let y = 200.0 + (i as f32 / 3.0).floor() * 150.0;
                    player.screen_position = Vector2f::new(x, y);
                    player.position = Vector3f::new(x, y, 0.0);

                    fake_players.push(player);
                }

                if let Ok(mut overlay) = overlay_clone.try_lock() {
                    overlay.update_players(fake_players, Team::CounterTerrorist);
                }

                thread::sleep(Duration::from_millis(100)); // Update demo at 10 FPS
            }
        });
    }

    // Start GUI
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_fullscreen(true)
            .with_decorations(false)
            .with_transparent(true)
            .with_always_on_top()
            .with_resizable(false)
            .with_window_level(egui::WindowLevel::AlwaysOnTop),
        ..Default::default()
    };

    eframe::run_native(
        "CS2 ESP",
        options,
        Box::new(move |_cc| {
            Ok(Box::new(EspOverlay::new()))
        }),
    ).map_err(|e| anyhow::anyhow!("GUI error: {}", e))?;

    Ok(())
}
