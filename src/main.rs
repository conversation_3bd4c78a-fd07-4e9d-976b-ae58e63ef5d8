mod entities;
mod esp;
mod esp_overlay;
mod config_window;
mod overlay_window;
mod sdl_overlay;
mod math;
mod memory;
mod offsets;

use anyhow::Result;
use eframe::egui;
use esp::EspEngine;
use overlay_window::OverlayWindow;
use sdl_overlay::SdlOverlay;
use entities::{EspConfig, Player, Team};
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

fn main() -> Result<()> {
    println!("CS2 External ESP - Educational Purpose Only");
    println!("Make sure CS2 is running with -insecure flag!");
    println!("Press INSERT to toggle menu");

    // Safety check - ensure this is for educational purposes
    println!("\n⚠️  IMPORTANT SAFETY NOTICE ⚠️");
    println!("This tool is for educational purposes only!");
    println!("Only use in private games with bots and -insecure flag!");
    println!("Do NOT use in public matchmaking!");

    // Wait for user confirmation
    println!("\nOptions:");
    println!("1. Press Enter to try connecting to CS2 (egui overlay)");
    println!("2. Type 'demo' and press Enter for demo mode (egui overlay)");
    println!("3. Type 'sdl' and press Enter for SDL2 overlay (better input passthrough)");
    print!("Choice: ");

    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;
    let input = input.trim().to_lowercase();
    let demo_mode = input == "demo";
    let use_sdl = input == "sdl";

    // Handle SDL overlay mode
    if use_sdl {
        println!("🚀 Starting SDL2 overlay with demo players");
        return run_sdl_overlay();
    }

    // Initialize ESP engine or demo mode
    let esp_engine = if demo_mode {
        println!("🎮 Starting in demo mode - ESP will show fake players");
        // In demo mode, we'll create fake players in the overlay
        None
    } else {
        match EspEngine::new() {
            Ok(engine) => {
                println!("✅ Successfully attached to CS2 process");
                Some(Arc::new(Mutex::new(engine)))
            }
            Err(e) => {
                eprintln!("❌ Failed to attach to CS2: {}", e);
                eprintln!("Make sure CS2 is running and you have the necessary permissions");
                eprintln!("Tip: Try running with 'demo' for demo mode or 'sdl' for SDL overlay");
                return Err(e);
            }
        }
    };

    // Create shared data
    let config = Arc::new(Mutex::new(EspConfig::default()));
    let players = Arc::new(Mutex::new(Vec::<Player>::new()));
    let local_team = Arc::new(Mutex::new(Team::None));

    // Start ESP update thread
    let players_clone = Arc::clone(&players);
    let local_team_clone = Arc::clone(&local_team);

    if let Some(esp_engine) = esp_engine {
        let esp_engine_clone = Arc::clone(&esp_engine);
        thread::spawn(move || {
            let mut error_count = 0;
            let mut last_error_print = std::time::Instant::now();

            loop {
                if let Ok(mut engine) = esp_engine_clone.try_lock() {
                    if let Err(e) = engine.update() {
                        error_count += 1;

                        // Only print errors every 5 seconds to avoid spam
                        if last_error_print.elapsed() > Duration::from_secs(5) {
                            eprintln!("ESP update error ({}): {}", error_count, e);
                            eprintln!("Make sure CS2 is running and you have proper permissions");
                            last_error_print = std::time::Instant::now();
                            error_count = 0;
                        }

                        thread::sleep(Duration::from_millis(1000)); // Wait longer on error
                        continue;
                    }

                    // Reset error count on successful update
                    error_count = 0;

                    if engine.is_ready() {
                        let engine_players = engine.get_players().clone();
                        let engine_local_team = engine.get_local_team();

                        if let Ok(mut players) = players_clone.try_lock() {
                            *players = engine_players;
                        }
                        if let Ok(mut local_team) = local_team_clone.try_lock() {
                            *local_team = engine_local_team;
                        }
                    }
                }

                thread::sleep(Duration::from_millis(16)); // ~60 FPS
            }
        });
    } else {
        // Demo mode - create fake players
        thread::spawn(move || {
            use crate::entities::{Vector2f, Vector3f};

            loop {
                let mut fake_players = Vec::new();

                // Create some fake players for demo
                for i in 0..6 {
                    let mut player = Player::new();
                    player.entity_index = i;
                    player.health = 100 - (i * 15);
                    player.team = if i % 2 == 0 { Team::Terrorist } else { Team::CounterTerrorist };
                    player.is_alive = true;
                    player.is_valid = true;
                    player.name = format!("DemoPlayer{}", i + 1);

                    // Set screen positions in a grid
                    let x = 200.0 + (i as f32 % 3.0) * 200.0;
                    let y = 200.0 + (i as f32 / 3.0).floor() * 150.0;
                    player.screen_position = Vector2f::new(x, y);
                    player.position = Vector3f::new(x, y, 0.0);

                    fake_players.push(player);
                }

                if let Ok(mut players) = players_clone.try_lock() {
                    *players = fake_players;
                }
                if let Ok(mut local_team) = local_team_clone.try_lock() {
                    *local_team = Team::CounterTerrorist;
                }

                thread::sleep(Duration::from_millis(100)); // Update demo at 10 FPS
            }
        });
    }

    // Start single overlay window with integrated config (main thread)
    let overlay_options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_fullscreen(true)
            .with_decorations(false)
            .with_transparent(true)
            .with_always_on_top()
            .with_resizable(false)
            .with_title("CS2 ESP Overlay")
            .with_active(false)  // Don't take focus
            .with_visible(true)
            .with_window_type(egui::X11WindowType::Tooltip) // Tooltip type for OSD behavior
            .with_taskbar(false), // Don't show in taskbar
        ..Default::default()
    };

    eframe::run_native(
        "CS2 ESP Overlay",
        overlay_options,
        Box::new(move |_cc| {
            Ok(Box::new(OverlayWindow::new(
                Arc::clone(&config),
                Arc::clone(&players),
                Arc::clone(&local_team),
            )))
        }),
    ).map_err(|e| anyhow::anyhow!("Overlay error: {}", e))?;

    Ok(())
}

fn run_sdl_overlay() -> Result<()> {
    // Create shared data for SDL overlay
    let config = Arc::new(Mutex::new(EspConfig::default()));
    let players = Arc::new(Mutex::new(Vec::<Player>::new()));
    let local_team = Arc::new(Mutex::new(Team::CounterTerrorist));

    // Start demo player generation thread
    let players_clone = Arc::clone(&players);
    let local_team_clone = Arc::clone(&local_team);

    thread::spawn(move || {
        use crate::entities::{Vector2f, Vector3f};

        loop {
            let mut fake_players = Vec::new();

            // Create some fake players for demo
            for i in 0..6 {
                let mut player = Player::new();
                player.entity_index = i;
                player.health = 100 - (i * 15);
                player.team = if i % 2 == 0 { Team::Terrorist } else { Team::CounterTerrorist };
                player.is_alive = true;
                player.is_valid = true;
                player.name = format!("DemoPlayer{}", i + 1);

                // Set screen positions in a grid
                let x = 300.0 + (i as f32 % 3.0) * 200.0;
                let y = 300.0 + (i as f32 / 3.0).floor() * 150.0;
                player.screen_position = Vector2f::new(x, y);
                player.position = Vector3f::new(x, y, 0.0);

                fake_players.push(player);
            }

            if let Ok(mut players) = players_clone.try_lock() {
                *players = fake_players;
            }
            if let Ok(mut local_team) = local_team_clone.try_lock() {
                *local_team = Team::CounterTerrorist;
            }

            thread::sleep(Duration::from_millis(100)); // Update demo at 10 FPS
        }
    });

    // Create and run SDL overlay
    let mut overlay = SdlOverlay::new(config, players, local_team)?;
    overlay.run()?;

    Ok(())
}
