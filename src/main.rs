mod entities;
mod esp;
mod esp_overlay;
mod math;
mod memory;
mod offsets;

use anyhow::Result;
use eframe::egui;
use esp::EspEngine;
use esp_overlay::EspOverlay;
use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;

fn main() -> Result<()> {
    println!("CS2 External ESP - Educational Purpose Only");
    println!("Make sure CS2 is running with -insecure flag!");
    println!("Press INSERT to toggle menu");

    // Safety check - ensure this is for educational purposes
    println!("\n⚠️  IMPORTANT SAFETY NOTICE ⚠️");
    println!("This tool is for educational purposes only!");
    println!("Only use in private games with bots and -insecure flag!");
    println!("Do NOT use in public matchmaking!");

    // Wait for user confirmation
    println!("\nPress Enter to continue...");
    let mut input = String::new();
    std::io::stdin().read_line(&mut input)?;

    // Initialize ESP engine
    let esp_engine = match EspEngine::new() {
        Ok(engine) => {
            println!("✅ Successfully attached to CS2 process");
            Arc::new(Mutex::new(engine))
        }
        Err(e) => {
            eprintln!("❌ Failed to attach to CS2: {}", e);
            eprintln!("Make sure CS2 is running and you have the necessary permissions");
            return Err(e);
        }
    };

    // Create overlay
    let overlay = Arc::new(Mutex::new(EspOverlay::new()));

    // Start ESP update thread
    let esp_engine_clone = Arc::clone(&esp_engine);
    let overlay_clone = Arc::clone(&overlay);

    thread::spawn(move || {
        loop {
            if let Ok(mut engine) = esp_engine_clone.try_lock() {
                if let Err(e) = engine.update() {
                    eprintln!("ESP update error: {}", e);
                    thread::sleep(Duration::from_millis(100));
                    continue;
                }

                if engine.is_ready() {
                    let players = engine.get_players().clone();
                    let local_team = engine.get_local_team();

                    if let Ok(mut overlay) = overlay_clone.try_lock() {
                        overlay.update_players(players, local_team);
                    }
                }
            }

            thread::sleep(Duration::from_millis(16)); // ~60 FPS
        }
    });

    // Start GUI
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([1920.0, 1080.0])
            .with_decorations(false)
            .with_transparent(true)
            .with_always_on_top()
            .with_resizable(false),
        ..Default::default()
    };

    eframe::run_native(
        "CS2 ESP",
        options,
        Box::new(move |_cc| {
            Ok(Box::new(EspOverlay::new()))
        }),
    ).map_err(|e| anyhow::anyhow!("GUI error: {}", e))?;

    Ok(())
}
