// Overlay module for always-on-top, borderless window using winit and pixels
// NOTE: True click-through overlays are not generally possible on Wayland due to security restrictions.
// This will create a borderless, always-on-top window for ESP drawing.

use winit::{event::Event, event_loop::ControlFlow, event_loop::EventLoop, window::{WindowBuilder, WindowLevel}};
#[cfg(target_os = "linux")]
use winit::platform::unix::WindowBuilderExtUnix;
use pixels::{Pixels, SurfaceTexture};
use crate::player::Player;

/// Run with: env WAYLAND_DISPLAY= XDG_SESSION_TYPE=x11 cargo run
/// This will force X11 mode for winit and allow X11-specific hacks.
pub fn run_overlay(players: Vec<Player>) {
    println!("[overlay] Starting overlay window (X11 recommended for click-through, always-on-top, and transparency)");

    let event_loop = EventLoop::new();
    let mut builder = WindowBuilder::new();
    builder = builder
        .with_title("CS2 ESP Overlay")
        .with_decorations(false)
        .with_transparent(true)
        .with_window_level(WindowLevel::AlwaysOnTop)
        .with_maximized(true);

    #[cfg(target_os = "linux")]
    {
        // X11-specific: set override redirect for true borderless, and try to set always-on-top and click-through
        use winit::platform::unix::WindowBuilderExtUnix;
        builder = builder.with_override_redirect(true);
    }

    let window = builder
        .build(&event_loop)
        .expect("Failed to create overlay window");

    #[cfg(target_os = "linux")]
    {
        // X11 click-through: set input shape to empty (requires x11 crate)
        use raw_window_handle::HasRawWindowHandle;
        if let raw_window_handle::RawWindowHandle::Xlib(handle) = window.raw_window_handle() {
            if let (Some(display), Some(window_id)) = (handle.display, handle.window) {
                unsafe {
                    use x11::xlib;
                    let dpy = display as *mut xlib::Display;
                    let win = window_id as u64;
                    // Set input shape to empty for click-through
                    let shape = x11::xlib::XShapeCombineRectangles;
                    if let Ok(lib) = libloading::Library::new("libXext.so.6") {
                        if let Ok(shape_fn) = lib.get::<unsafe extern "C" fn(*mut xlib::Display, u64, i32, i32, i32, *const x11::xlib::XRectangle, i32, i32)>(b"XShapeCombineRectangles") {
                            shape_fn(dpy, win, 2, 0, 0, std::ptr::null(), 0, 0);
                        }
                    }
                }
            }
        }
    }

    let mut size = window.inner_size();
    let surface_texture = SurfaceTexture::new(size.width, size.height, &window);
    let mut pixels = Pixels::new(size.width, size.height, surface_texture).expect("Failed to create pixel buffer");

    // Dummy world-to-screen projection (replace with real one for accuracy)
    fn world_to_screen(x: f32, y: f32, _z: f32, width: u32, height: u32) -> Option<(i32, i32)> {
        // This is a placeholder. Real projection needs view/projection matrices from the game.
        // For demo, just center and scale down world coords.
        let sx = (x / 10.0) as i32 + (width as i32 / 2);
        let sy = (y / 10.0) as i32 + (height as i32 / 2);
        Some((sx, sy))
    }

    event_loop.run(move |event, _window_target, control_flow| {
        *control_flow = ControlFlow::Poll;
        match event {
            Event::WindowEvent { event, .. } => match event {
                winit::event::WindowEvent::CloseRequested => {
                    *control_flow = ControlFlow::Exit;
                }
                winit::event::WindowEvent::Resized(new_size) => {
                    size = new_size;
                    pixels.resize_surface(new_size.width, new_size.height).unwrap();
                }
                _ => {}
            },
            Event::RedrawRequested(_) => {
                // Clear to transparent
                for pixel in pixels.frame_mut().chunks_exact_mut(4) {
                    pixel[0] = 0; // R
                    pixel[1] = 0; // G
                    pixel[2] = 0; // B
                    pixel[3] = 0; // A
                }
                // Draw ESP boxes for each player
                let (w, h) = (size.width, size.height);
                for p in &players {
                    if let Some((sx, sy)) = world_to_screen(p.x, p.y, p.z, w, h) {
                        draw_box(pixels.frame_mut(), w, h, sx, sy, 40, 80, [255, 0, 0, 200]);
                    }
                }
                pixels.render().unwrap();
            }
            Event::MainEventsCleared => {
                window.request_redraw();
            }
            _ => {}
        }
    });
}

// Draw a rectangle (box) at (x, y) with width w and height h and color rgba
fn draw_box(frame: &mut [u8], win_w: u32, win_h: u32, x: i32, y: i32, box_w: i32, box_h: i32, color: [u8; 4]) {
    for dx in 0..box_w {
        for dy in [0, box_h - 1] {
            set_pixel(frame, win_w, win_h, x + dx, y + dy, color);
        }
    }
    for dy in 0..box_h {
        for dx in [0, box_w - 1] {
            set_pixel(frame, win_w, win_h, x + dx, y + dy, color);
        }
    }
}

fn set_pixel(frame: &mut [u8], win_w: u32, win_h: u32, x: i32, y: i32, color: [u8; 4]) {
    if x < 0 || y < 0 || x >= win_w as i32 || y >= win_h as i32 {
        return;
    }
    let idx = ((y as u32 * win_w + x as u32) * 4) as usize;
    if idx + 3 < frame.len() {
        frame[idx..idx + 4].copy_from_slice(&color);
    }
}
