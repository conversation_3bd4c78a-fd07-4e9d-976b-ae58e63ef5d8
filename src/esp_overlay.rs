use crate::entities::{Player, EspConfig, BoundingBox, Vector2f, Team};
use eframe::egui::{self, Color32, FontId, Pos2, Rect, Stroke, Vec2};
use eframe::{App, Frame};

pub struct EspOverlay {
    pub config: EspConfig,
    pub players: Vec<Player>,
    pub local_team: Team,
    pub show_menu: bool,
    pub menu_key_pressed: bool,
}

impl EspOverlay {
    pub fn new() -> Self {
        Self {
            config: EspConfig::default(),
            players: Vec::new(),
            local_team: Team::None,
            show_menu: false,
            menu_key_pressed: false,
        }
    }

    pub fn update_players(&mut self, players: Vec<Player>, local_team: Team) {
        self.players = players;
        self.local_team = local_team;
    }

    fn draw_esp_box(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox) {
        if !self.config.draw_boxes {
            return;
        }

        let color = if self.config.enemies_only && !player.is_enemy(self.local_team) {
            return;
        } else {
            Color32::from_rgba_unmultiplied(
                (player.team.color()[0] * 255.0) as u8,
                (player.team.color()[1] * 255.0) as u8,
                (player.team.color()[2] * 255.0) as u8,
                200,
            )
        };

        let stroke = Stroke::new(self.config.box_thickness, color);
        
        // Draw main box
        let rect = Rect::from_min_max(
            Pos2::new(bbox.top_left.x, bbox.top_left.y),
            Pos2::new(bbox.bottom_right.x, bbox.bottom_right.y),
        );
        
        ui.painter().rect_stroke(rect, 0.0, stroke);
        
        // Draw health bar if enabled
        if self.config.draw_health && player.health > 0 {
            self.draw_health_bar(ui, player, bbox);
        }
    }

    fn draw_health_bar(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox) {
        let health_percentage = player.health_percentage();
        let bar_width = 4.0;
        let bar_height = bbox.height;
        
        // Background bar
        let bg_rect = Rect::from_min_size(
            Pos2::new(bbox.top_left.x - bar_width - 2.0, bbox.top_left.y),
            Vec2::new(bar_width, bar_height),
        );
        ui.painter().rect_filled(bg_rect, 0.0, Color32::from_rgba_unmultiplied(0, 0, 0, 150));
        
        // Health bar
        let health_height = bar_height * health_percentage;
        let health_rect = Rect::from_min_size(
            Pos2::new(bbox.top_left.x - bar_width - 2.0, bbox.bottom_right.y - health_height),
            Vec2::new(bar_width, health_height),
        );
        
        let health_color = if health_percentage > 0.6 {
            Color32::GREEN
        } else if health_percentage > 0.3 {
            Color32::YELLOW
        } else {
            Color32::RED
        };
        
        ui.painter().rect_filled(health_rect, 0.0, health_color);
        
        // Health text
        let health_text = format!("{}", player.health);
        let text_pos = Pos2::new(
            bbox.top_left.x - bar_width - 15.0,
            bbox.bottom_right.y - health_height - 8.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::RIGHT_CENTER,
            health_text,
            FontId::monospace(10.0),
            Color32::WHITE,
        );
    }

    fn draw_player_name(&self, ui: &mut egui::Ui, player: &Player, bbox: &BoundingBox) {
        if !self.config.draw_names || player.name.is_empty() {
            return;
        }

        let text_pos = Pos2::new(
            bbox.top_left.x + bbox.width / 2.0,
            bbox.top_left.y - 15.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::CENTER_BOTTOM,
            &player.name,
            FontId::proportional(self.config.text_size),
            Color32::WHITE,
        );
    }

    fn draw_distance(&self, ui: &mut egui::Ui, _player: &Player, bbox: &BoundingBox, distance: f32) {
        if !self.config.draw_distance {
            return;
        }

        let distance_text = format!("{:.0}m", distance);
        let text_pos = Pos2::new(
            bbox.top_left.x + bbox.width / 2.0,
            bbox.bottom_right.y + 5.0,
        );
        
        ui.painter().text(
            text_pos,
            egui::Align2::CENTER_TOP,
            distance_text,
            FontId::monospace(10.0),
            Color32::LIGHT_GRAY,
        );
    }

    fn draw_menu(&mut self, ui: &mut egui::Ui) {
        egui::Window::new("ESP Configuration")
            .default_width(300.0)
            .show(ui.ctx(), |ui| {
                ui.heading("ESP Settings");
                
                ui.checkbox(&mut self.config.enabled, "Enable ESP");
                ui.separator();
                
                ui.checkbox(&mut self.config.draw_boxes, "Draw Boxes");
                ui.checkbox(&mut self.config.draw_health, "Draw Health");
                ui.checkbox(&mut self.config.draw_names, "Draw Names");
                ui.checkbox(&mut self.config.draw_distance, "Draw Distance");
                ui.checkbox(&mut self.config.enemies_only, "Enemies Only");
                
                ui.separator();
                
                ui.add(egui::Slider::new(&mut self.config.box_thickness, 1.0..=5.0).text("Box Thickness"));
                ui.add(egui::Slider::new(&mut self.config.text_size, 8.0..=24.0).text("Text Size"));
                ui.add(egui::Slider::new(&mut self.config.max_distance, 100.0..=2000.0).text("Max Distance"));
                
                ui.separator();
                
                if ui.button("Save Config").clicked() {
                    // TODO: Implement config saving
                }
                
                if ui.button("Load Config").clicked() {
                    // TODO: Implement config loading
                }
            });
    }
}

impl App for EspOverlay {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut Frame) {
        // Handle menu toggle (INSERT key)
        ctx.input(|i| {
            if i.key_pressed(egui::Key::Insert) && !self.menu_key_pressed {
                self.show_menu = !self.show_menu;
                self.menu_key_pressed = true;
            } else if !i.key_pressed(egui::Key::Insert) {
                self.menu_key_pressed = false;
            }
        });

        // Set up transparent background
        let frame = egui::Frame::none().fill(Color32::TRANSPARENT);
        
        egui::CentralPanel::default().frame(frame).show(ctx, |ui| {
            if !self.config.enabled {
                return;
            }

            // Draw ESP for each player
            for player in &self.players {
                if !player.should_draw() {
                    continue;
                }

                if self.config.enemies_only && !player.is_enemy(self.local_team) {
                    continue;
                }

                // Calculate bounding box from screen position
                // This is a simplified version - in practice you'd use the world-to-screen conversion
                let bbox = BoundingBox::new(
                    Vector2f::new(player.screen_position.x - 30.0, player.screen_position.y - 60.0),
                    Vector2f::new(player.screen_position.x + 30.0, player.screen_position.y + 10.0),
                );

                self.draw_esp_box(ui, player, &bbox);
                self.draw_player_name(ui, player, &bbox);
                
                // Calculate distance (placeholder)
                let distance = 100.0; // This would be calculated from actual positions
                self.draw_distance(ui, player, &bbox, distance);
            }
        });

        // Draw menu if enabled
        if self.show_menu {
            egui::CentralPanel::default().show(ctx, |ui| {
                self.draw_menu(ui);
            });
        }

        // Request repaint for smooth rendering
        ctx.request_repaint();
    }
}
