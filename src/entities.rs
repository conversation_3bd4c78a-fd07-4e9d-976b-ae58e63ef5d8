use nalgebra::{Vector3, Matrix4};
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq)]
pub struct Vector3f {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Vector3f {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0, 0.0)
    }

    pub fn to_nalgebra(&self) -> Vector3<f32> {
        Vector3::new(self.x, self.y, self.z)
    }
}

impl From<[f32; 3]> for Vector3f {
    fn from(arr: [f32; 3]) -> Self {
        Self::new(arr[0], arr[1], arr[2])
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct Vector2f {
    pub x: f32,
    pub y: f32,
}

impl Vector2f {
    pub fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }

    pub fn zero() -> Self {
        Self::new(0.0, 0.0)
    }
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>Eq, Eq)]
pub enum Team {
    None = 0,
    Spectator = 1,
    Terrorist = 2,
    CounterTerrorist = 3,
}

impl From<u8> for Team {
    fn from(value: u8) -> Self {
        match value {
            0 => Team::None,
            1 => Team::Spectator,
            2 => Team::Terrorist,
            3 => Team::CounterTerrorist,
            _ => Team::None,
        }
    }
}

impl Team {
    pub fn is_enemy(&self, other: &Team) -> bool {
        match (self, other) {
            (Team::Terrorist, Team::CounterTerrorist) => true,
            (Team::CounterTerrorist, Team::Terrorist) => true,
            _ => false,
        }
    }

    pub fn color(&self) -> [f32; 4] {
        match self {
            Team::Terrorist => [1.0, 0.8, 0.0, 1.0], // Orange
            Team::CounterTerrorist => [0.0, 0.5, 1.0, 1.0], // Blue
            Team::Spectator => [0.7, 0.7, 0.7, 1.0], // Gray
            Team::None => [1.0, 1.0, 1.0, 1.0], // White
        }
    }
}

#[derive(Debug, Clone)]
pub struct Player {
    pub entity_index: i32,
    pub controller_address: usize,
    pub pawn_address: usize,
    pub position: Vector3f,
    pub screen_position: Vector2f,
    pub health: i32,
    pub max_health: i32,
    pub armor: i32,
    pub team: Team,
    pub is_alive: bool,
    pub is_valid: bool,
    pub name: String,
    pub dormant: bool,
}

impl Player {
    pub fn new() -> Self {
        Self {
            entity_index: -1,
            controller_address: 0,
            pawn_address: 0,
            position: Vector3f::zero(),
            screen_position: Vector2f::zero(),
            health: 0,
            max_health: 100,
            armor: 0,
            team: Team::None,
            is_alive: false,
            is_valid: false,
            name: String::new(),
            dormant: false,
        }
    }

    pub fn is_enemy(&self, local_team: Team) -> bool {
        self.team.is_enemy(&local_team)
    }

    pub fn health_percentage(&self) -> f32 {
        if self.max_health <= 0 {
            return 0.0;
        }
        (self.health as f32 / self.max_health as f32).clamp(0.0, 1.0)
    }

    pub fn should_draw(&self) -> bool {
        self.is_valid && self.is_alive && !self.dormant && self.health > 0
    }
}

#[derive(Debug, Clone)]
pub struct LocalPlayer {
    pub player: Player,
    pub view_matrix: Matrix4<f32>,
    pub screen_width: f32,
    pub screen_height: f32,
}

impl LocalPlayer {
    pub fn new() -> Self {
        Self {
            player: Player::new(),
            view_matrix: Matrix4::identity(),
            screen_width: 1920.0,
            screen_height: 1080.0,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EspConfig {
    pub enabled: bool,
    pub draw_boxes: bool,
    pub draw_health: bool,
    pub draw_names: bool,
    pub draw_distance: bool,
    pub enemies_only: bool,
    pub box_thickness: f32,
    pub text_size: f32,
    pub max_distance: f32,
}

impl Default for EspConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            draw_boxes: true,
            draw_health: true,
            draw_names: true,
            draw_distance: false,
            enemies_only: true,
            box_thickness: 2.0,
            text_size: 14.0,
            max_distance: 1000.0,
        }
    }
}

// Bounding box for ESP rendering
#[derive(Debug, Clone, Copy)]
pub struct BoundingBox {
    pub top_left: Vector2f,
    pub bottom_right: Vector2f,
    pub width: f32,
    pub height: f32,
}

impl BoundingBox {
    pub fn new(top_left: Vector2f, bottom_right: Vector2f) -> Self {
        let width = bottom_right.x - top_left.x;
        let height = bottom_right.y - top_left.y;
        Self {
            top_left,
            bottom_right,
            width,
            height,
        }
    }

    pub fn center(&self) -> Vector2f {
        Vector2f::new(
            self.top_left.x + self.width / 2.0,
            self.top_left.y + self.height / 2.0,
        )
    }
}
