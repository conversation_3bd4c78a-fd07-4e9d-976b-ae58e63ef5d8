use crate::entities::{Vector2f, Vector3f, BoundingBox};
use nalgebra::{Matrix4, Vector4};

pub struct WorldToScreen {
    pub view_matrix: Matrix4<f32>,
    pub screen_width: f32,
    pub screen_height: f32,
}

impl WorldToScreen {
    pub fn new(view_matrix: Matrix4<f32>, screen_width: f32, screen_height: f32) -> Self {
        Self {
            view_matrix,
            screen_width,
            screen_height,
        }
    }

    /// Convert 3D world coordinates to 2D screen coordinates
    pub fn world_to_screen(&self, world_pos: Vector3f) -> Option<Vector2f> {
        // Convert to homogeneous coordinates
        let world_vec = Vector4::new(world_pos.x, world_pos.y, world_pos.z, 1.0);
        
        // Transform by view matrix
        let clip_coords = self.view_matrix * world_vec;
        
        // Check if point is behind camera
        if clip_coords.w < 0.1 {
            return None;
        }
        
        // Perform perspective divide
        let ndc_x = clip_coords.x / clip_coords.w;
        let ndc_y = clip_coords.y / clip_coords.w;
        
        // Check if point is within screen bounds (with some tolerance)
        if ndc_x < -1.2 || ndc_x > 1.2 || ndc_y < -1.2 || ndc_y > 1.2 {
            return None;
        }
        
        // Convert to screen coordinates
        let screen_x = (ndc_x + 1.0) * 0.5 * self.screen_width;
        let screen_y = (1.0 - ndc_y) * 0.5 * self.screen_height;
        
        Some(Vector2f::new(screen_x, screen_y))
    }

    /// Calculate a bounding box for a player at the given world position
    pub fn calculate_bounding_box(&self, world_pos: Vector3f, player_height: f32) -> Option<BoundingBox> {
        // Calculate head and feet positions
        let feet_pos = world_pos;
        let head_pos = Vector3f::new(world_pos.x, world_pos.y, world_pos.z + player_height);
        
        // Convert to screen coordinates
        let feet_screen = self.world_to_screen(feet_pos)?;
        let head_screen = self.world_to_screen(head_pos)?;
        
        // Calculate box dimensions
        let box_height = (feet_screen.y - head_screen.y).abs();
        let box_width = box_height * 0.6; // Typical player width ratio
        
        // Calculate corners
        let center_x = (feet_screen.x + head_screen.x) * 0.5;
        let top_y = head_screen.y.min(feet_screen.y);
        let bottom_y = head_screen.y.max(feet_screen.y);
        
        let top_left = Vector2f::new(center_x - box_width * 0.5, top_y);
        let bottom_right = Vector2f::new(center_x + box_width * 0.5, bottom_y);
        
        Some(BoundingBox::new(top_left, bottom_right))
    }

    /// Calculate distance between two 3D points
    pub fn calculate_distance(pos1: Vector3f, pos2: Vector3f) -> f32 {
        let dx = pos1.x - pos2.x;
        let dy = pos1.y - pos2.y;
        let dz = pos1.z - pos2.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// Check if a screen position is visible on screen
    pub fn is_on_screen(&self, screen_pos: Vector2f) -> bool {
        screen_pos.x >= 0.0 
            && screen_pos.x <= self.screen_width 
            && screen_pos.y >= 0.0 
            && screen_pos.y <= self.screen_height
    }

    /// Update the view matrix and screen dimensions
    pub fn update(&mut self, view_matrix: Matrix4<f32>, screen_width: f32, screen_height: f32) {
        self.view_matrix = view_matrix;
        self.screen_width = screen_width;
        self.screen_height = screen_height;
    }
}

/// Helper function to create a view matrix from raw bytes
pub fn matrix_from_bytes(bytes: &[u8]) -> Option<Matrix4<f32>> {
    if bytes.len() < 64 {
        return None;
    }

    let mut matrix_data = [0.0f32; 16];
    for i in 0..16 {
        let byte_offset = i * 4;
        if byte_offset + 4 <= bytes.len() {
            matrix_data[i] = f32::from_le_bytes([
                bytes[byte_offset],
                bytes[byte_offset + 1],
                bytes[byte_offset + 2],
                bytes[byte_offset + 3],
            ]);
        }
    }

    Some(Matrix4::from_row_slice(&matrix_data))
}

/// Calculate the angle between two 3D points
pub fn calculate_angle(from: Vector3f, to: Vector3f) -> Vector2f {
    let delta = Vector3f::new(to.x - from.x, to.y - from.y, to.z - from.z);
    
    let distance = (delta.x * delta.x + delta.y * delta.y).sqrt();
    
    let yaw = delta.y.atan2(delta.x) * 180.0 / std::f32::consts::PI;
    let pitch = (-delta.z).atan2(distance) * 180.0 / std::f32::consts::PI;
    
    Vector2f::new(yaw, pitch)
}

/// Normalize an angle to [-180, 180] range
pub fn normalize_angle(angle: f32) -> f32 {
    let mut normalized = angle % 360.0;
    if normalized > 180.0 {
        normalized -= 360.0;
    } else if normalized < -180.0 {
        normalized += 360.0;
    }
    normalized
}

#[cfg(test)]
mod tests {
    use super::*;
    use nalgebra::Matrix4;

    #[test]
    fn test_world_to_screen_basic() {
        let view_matrix = Matrix4::identity();
        let w2s = WorldToScreen::new(view_matrix, 1920.0, 1080.0);
        
        // Test point at origin
        let world_pos = Vector3f::new(0.0, 0.0, 0.0);
        let screen_pos = w2s.world_to_screen(world_pos);
        
        assert!(screen_pos.is_some());
    }

    #[test]
    fn test_distance_calculation() {
        let pos1 = Vector3f::new(0.0, 0.0, 0.0);
        let pos2 = Vector3f::new(3.0, 4.0, 0.0);
        let distance = WorldToScreen::calculate_distance(pos1, pos2);
        
        assert!((distance - 5.0).abs() < 0.001);
    }

    #[test]
    fn test_angle_calculation() {
        let from = Vector3f::new(0.0, 0.0, 0.0);
        let to = Vector3f::new(1.0, 0.0, 0.0);
        let angle = calculate_angle(from, to);
        
        assert!((angle.x - 0.0).abs() < 0.001); // Should be 0 degrees yaw
    }
}
