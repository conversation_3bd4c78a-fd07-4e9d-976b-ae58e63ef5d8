# CS2 External ESP - Educational Project

## ⚠️ IMPORTANT DISCLAIMER ⚠️

**THIS PROJECT IS FOR EDUCATIONAL PURPOSES ONLY!**

- Only use in private games with bots
- Always run CS2 with the `-insecure` flag to disable VAC
- Do NOT use in public matchmaking or competitive games
- This is for learning game hacking concepts and reverse engineering

## What is this?

This is a simple external ESP (Extra Sensory Perception) for Counter-Strike 2, written in Rust. It demonstrates:

- External memory reading from game processes
- 3D to 2D coordinate conversion (world-to-screen)
- Overlay rendering with transparent windows
- Game entity parsing and player detection
- Real-time data visualization

## Features

- **Player ESP Boxes**: Draw colored boxes around players
- **Health Bars**: Visual health indicators with percentage-based colors
- **Team Colors**: Different colors for Terrorists (orange) and Counter-Terrorists (blue)
- **Configurable Settings**: Toggle features on/off via menu
- **Menu System**: Press INSERT to open configuration menu
- **Demo Mode**: Test ESP functionality without CS2 running
- **Fullscreen Overlay**: Covers entire screen, always on top
- **Input Passthrough**: Doesn't interfere with game input (when menu closed)
- **Error Handling**: Graceful handling of CS2 not running
- **Linux Support**: Native Linux implementation using /proc/PID/mem

## How to Build

1. Make sure you have Rust installed
2. Clone this repository
3. Run: `cargo build --release`

## How to Use

### Option 1: Demo Mode (Recommended for Testing)

1. **Run the ESP in demo mode**:
   ```bash
   cargo run --release
   ```

2. **When prompted, type `demo` and press Enter**

3. **The ESP will show fake players to demonstrate functionality**

4. **Controls**:
   - Press `INSERT` to toggle the configuration menu
   - Configure ESP settings in the menu
   - Close the application to exit

### Option 2: Real CS2 Integration

1. **Start CS2 with `-insecure` flag**:
   ```
   steam://rungame/730/76561202255233023/+exec%20autoexec.cfg%20-insecure
   ```

2. **Join a private game with bots**

3. **Run the ESP with elevated permissions**:
   ```bash
   sudo cargo run --release
   ```

4. **When prompted, press Enter (don't type 'demo')**

5. **Controls**:
   - Press `INSERT` to toggle the configuration menu
   - Configure ESP settings in the menu
   - Close the application to exit

## Project Structure

```
src/
├── main.rs           # Main application entry point
├── entities.rs       # Player and game entity structures
├── esp.rs           # ESP engine and game memory reading
├── esp_overlay.rs   # GUI overlay and rendering
├── math.rs          # 3D math and world-to-screen conversion
├── memory.rs        # Process memory reading (Linux)
└── offsets.rs       # CS2 memory offsets
```

## Technical Details

### Memory Reading
- Uses `/proc/PID/mem` on Linux for external memory access
- Reads game structures directly from CS2's memory space
- Parses entity lists to find player information

### Rendering
- Uses `egui` for the overlay GUI
- Transparent window that renders over the game
- Real-time updates at ~60 FPS

### World-to-Screen Conversion
- Reads the game's view matrix
- Converts 3D world coordinates to 2D screen positions
- Handles perspective projection and clipping

## Learning Resources

This project demonstrates several important concepts:

1. **External Game Hacking**: Reading memory from external processes
2. **Reverse Engineering**: Understanding game data structures
3. **3D Graphics**: Matrix transformations and coordinate systems
4. **GUI Programming**: Creating overlays and user interfaces
5. **Systems Programming**: Low-level memory access and process interaction

## Safety and Ethics

- This tool is designed for educational purposes only
- Always use in controlled environments (private servers with bots)
- Never use in competitive or public games
- Respect game terms of service and anti-cheat systems
- Use the `-insecure` flag to disable VAC when testing

## Limitations

- Currently Linux-only (uses `/proc/PID/mem`)
- Simplified offset management (real cheats use dynamic offset updating)
- Basic entity parsing (doesn't handle all game states)
- No advanced features like aimbot or triggerbot

## Contributing

This is an educational project. If you want to contribute:

1. Keep the educational focus
2. Add comments explaining complex concepts
3. Maintain the safety disclaimers
4. Don't add features that could be used maliciously

## Legal Notice

This software is provided for educational purposes only. The authors are not responsible for any misuse of this software. Always respect game terms of service and applicable laws.
