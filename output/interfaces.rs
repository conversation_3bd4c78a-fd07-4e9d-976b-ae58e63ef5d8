// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#![allow(non_upper_case_globals, unused)]

pub mod cs2_dumper {
    pub mod interfaces {
        // Module: libanimationsystem.so
        pub mod libanimationsystem_so {
            pub const AnimationSystemUtils_001: usize = 0x1B4F70;
            pub const AnimationSystem_001: usize = 0x1B4D60;
        }
        // Module: libclient.so
        pub mod libclient_so {
            pub const ClientToolsInfo_001: usize = 0xE07340;
            pub const EmptyWorldService001_Client: usize = 0xACDC90;
            pub const GameClientExports001: usize = 0xE06F10;
            pub const LegacyGameUI001: usize = 0xFDF680;
            pub const Source2Client002: usize = 0xE06F70;
            pub const Source2ClientConfig001: usize = 0xAB8490;
            pub const Source2ClientPrediction001: usize = 0xE96E90;
            pub const Source2ClientUI001: usize = 0xF6CB90;
        }
        // Module: libengine2.so
        pub mod libengine2_so {
            pub const BenchmarkService001: usize = 0x2239C0;
            pub const BugService001: usize = 0x21FB80;
            pub const ClientServerEngineLoopService_001: usize = 0x1EEF60;
            pub const EngineGameUI001: usize = 0x3BE430;
            pub const EngineServiceMgr001: usize = 0x1DF0D0;
            pub const GameEventSystemClientV001: usize = 0x1E4480;
            pub const GameEventSystemServerV001: usize = 0x1E4490;
            pub const GameResourceServiceClientV001: usize = 0x225310;
            pub const GameResourceServiceServerV001: usize = 0x225320;
            pub const GameUIService_001: usize = 0x22D550;
            pub const HostStateMgr001: usize = 0x1E9AA0;
            pub const INETSUPPORT_001: usize = 0x3879C0;
            pub const InputService_001: usize = 0x231C40;
            pub const KeyValueCache001: usize = 0x1EC3F0;
            pub const MapListService_001: usize = 0x246C50;
            pub const NetworkClientService_001: usize = 0x269BB0;
            pub const NetworkP2PService_001: usize = 0x27EEF0;
            pub const NetworkServerService_001: usize = 0x24AFC0;
            pub const NetworkService_001: usize = 0x24A6E0;
            pub const RenderService_001: usize = 0x2840D0;
            pub const ScreenshotService001: usize = 0x286C50;
            pub const SimpleEngineLoopService_001: usize = 0x2056D0;
            pub const SoundService_001: usize = 0x28AFE0;
            pub const Source2EngineToClient001: usize = 0x30C5F0;
            pub const Source2EngineToClientStringTable001: usize = 0x2DF0B0;
            pub const Source2EngineToServer001: usize = 0x333290;
            pub const Source2EngineToServerStringTable001: usize = 0x315C80;
            pub const SplitScreenService_001: usize = 0x292920;
            pub const StatsService_001: usize = 0x2962E0;
            pub const ToolService_001: usize = 0x29AF40;
            pub const VENGINE_GAMEUIFUNCS_VERSION005: usize = 0x3BDE80;
            pub const VProfService_001: usize = 0x29C7C0;
        }
        // Module: libfilesystem_stdio.so
        pub mod libfilesystem_stdio_so {
            pub const VAsyncFileSystem2_001: usize = 0x7DB90;
            pub const VFileSystem017: usize = 0x7DB80;
        }
        // Module: libhost.so
        pub mod libhost_so {
            pub const DebugDrawQueueManager001: usize = 0xC5180;
            pub const GameModelInfo001: usize = 0xC0190;
            pub const GameSystem2HostHook: usize = 0xC08C0;
            pub const HostUtils001: usize = 0xC0D40;
            pub const PredictionDiffManager001: usize = 0xC1CC0;
            pub const SaveRestoreDataVersion001: usize = 0xC3CC0;
            pub const SinglePlayerSharedMemory001: usize = 0xC3F80;
            pub const Source2Host001: usize = 0xC46E0;
        }
        // Module: libinputsystem.so
        pub mod libinputsystem_so {
            pub const InputStackSystemVersion001: usize = 0x11500;
            pub const InputSystemVersion001: usize = 0x129F0;
        }
        // Module: liblocalize.so
        pub mod liblocalize_so {
            pub const Localize_001: usize = 0x1D770;
        }
        // Module: libmatchmaking.so
        pub mod libmatchmaking_so {
            pub const GameTypes001: usize = 0xF5D70;
            pub const MATCHFRAMEWORK_001: usize = 0x1CF4C0;
        }
        // Module: libmaterialsystem2.so
        pub mod libmaterialsystem2_so {
            pub const FontManager_001: usize = 0x7DDF0;
            pub const MaterialUtils_001: usize = 0x67C30;
            pub const PostProcessingSystem_001: usize = 0x8D2C0;
            pub const TextLayout_001: usize = 0x8A850;
            pub const VMaterialSystem2_001: usize = 0x2C470;
        }
        // Module: libmeshsystem.so
        pub mod libmeshsystem_so {
            pub const MeshSystem001: usize = 0x97630;
        }
        // Module: libnetworksystem.so
        pub mod libnetworksystem_so {
            pub const FlattenedSerializersVersion001: usize = 0x163840;
            pub const NetworkMessagesVersion001: usize = 0x18C1C0;
            pub const NetworkSystemVersion001: usize = 0x1B5760;
            pub const SerializedEntitiesVersion001: usize = 0x1CF120;
        }
        // Module: libpanorama.so
        pub mod libpanorama_so {
            pub const PanoramaUIEngine001: usize = 0x2342D0;
        }
        // Module: libpanorama_text_pango.so
        pub mod libpanorama_text_pango_so {
            pub const PanoramaTextServices001: usize = 0xBB8E0;
        }
        // Module: libpanoramauiclient.so
        pub mod libpanoramauiclient_so {
            pub const PanoramaUIClient001: usize = 0x10A0D0;
        }
        // Module: libparticles.so
        pub mod libparticles_so {
            pub const ParticleSystemMgr003: usize = 0x212AF0;
        }
        // Module: libpulse_system.so
        pub mod libpulse_system_so {
            pub const IPulseSystem_001: usize = 0x402F0;
        }
        // Module: librendersystemvulkan.so
        pub mod librendersystemvulkan_so {
            pub const RenderDeviceMgr001: usize = 0x14E840;
            pub const RenderUtils_001: usize = 0xCD990;
        }
        // Module: libresourcesystem.so
        pub mod libresourcesystem_so {
            pub const ResourceSystem013: usize = 0x30F40;
        }
        // Module: libscenefilecache.so
        pub mod libscenefilecache_so {
            pub const ResponseRulesCache001: usize = 0x89040;
            pub const SceneFileCache002: usize = 0x85BA0;
        }
        // Module: libscenesystem.so
        pub mod libscenesystem_so {
            pub const RenderingPipelines_001: usize = 0x142660;
            pub const SceneSystem_002: usize = 0x186F90;
            pub const SceneUtils_001: usize = 0x206810;
        }
        // Module: libschemasystem.so
        pub mod libschemasystem_so {
            pub const SchemaSystem_001: usize = 0x20FC0;
        }
        // Module: libserver.so
        pub mod libserver_so {
            pub const EmptyWorldService001_Server: usize = 0xBD2840;
            pub const EntitySubclassUtilsV001: usize = 0x8B9640;
            pub const NavGameTest001: usize = 0x1118F10;
            pub const ServerToolsInfo_001: usize = 0xEE1440;
            pub const Source2GameClients001: usize = 0xEE13D0;
            pub const Source2GameDirector001: usize = 0x709580;
            pub const Source2GameEntities001: usize = 0xEE13C0;
            pub const Source2Server001: usize = 0xEE10C0;
            pub const Source2ServerConfig001: usize = 0xB6EA60;
            pub const customnavsystem001: usize = 0x83C060;
        }
        // Module: libsoundsystem.so
        pub mod libsoundsystem_so {
            pub const SoundOpSystem001: usize = 0x176F30;
            pub const SoundOpSystemEdit001: usize = 0xB6E30;
            pub const SoundSystem001: usize = 0x1E62D0;
            pub const VMixEditTool001: usize = 0x21A190;
        }
        // Module: libsteamaudio.so
        pub mod libsteamaudio_so {
            pub const SteamAudio001: usize = 0x32410;
        }
        // Module: libtier0.so
        pub mod libtier0_so {
            pub const TestScriptMgr001: usize = 0x1B9C40;
            pub const VEngineCvar007: usize = 0xF6200;
            pub const VProcessUtils002: usize = 0x1AD430;
            pub const VStringTokenSystem001: usize = 0x1DFD50;
        }
        // Module: libv8system.so
        pub mod libv8system_so {
            pub const Source2V8System001: usize = 0x1B450;
        }
        // Module: libvphysics2.so
        pub mod libvphysics2_so {
            pub const VPhysics2_Handle_Interface_001: usize = 0xC92A0;
            pub const VPhysics2_Interface_001: usize = 0xC8E90;
        }
        // Module: libvscript.so
        pub mod libvscript_so {
            pub const VScriptManager010: usize = 0x25DA0;
        }
        // Module: libworldrenderer.so
        pub mod libworldrenderer_so {
            pub const WorldRendererMgr001: usize = 0xAED00;
        }
        // Module: steamclient.so
        pub mod steamclient_so {
            pub const CLIENTENGINE_INTERFACE_VERSION005: usize = 0x1455CA0;
            pub const IVALIDATE001: usize = 0x1451AC0;
            pub const SteamClient006: usize = 0x111AED0;
            pub const SteamClient007: usize = 0x111AEE0;
            pub const SteamClient008: usize = 0x111AEF0;
            pub const SteamClient009: usize = 0x111AF00;
            pub const SteamClient010: usize = 0x111AF10;
            pub const SteamClient011: usize = 0x111AF20;
            pub const SteamClient012: usize = 0x111AF30;
            pub const SteamClient013: usize = 0x111AF40;
            pub const SteamClient014: usize = 0x111AF50;
            pub const SteamClient015: usize = 0x111AF60;
            pub const SteamClient016: usize = 0x111AF70;
            pub const SteamClient017: usize = 0x111AF80;
            pub const SteamClient018: usize = 0x111AF90;
            pub const SteamClient019: usize = 0x111AFA0;
            pub const SteamClient020: usize = 0x111AFB0;
            pub const SteamClient021: usize = 0x111AFC0;
            pub const SteamClient022: usize = 0x111AFD0;
            pub const p2pvoice002: usize = 0x1C9B1B0;
            pub const p2pvoicesingleton002: usize = 0x1C93900;
        }
    }
}
