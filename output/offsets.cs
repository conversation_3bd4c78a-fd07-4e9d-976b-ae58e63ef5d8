// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

namespace CS2Dumper.Offsets {
    // Module: libclient.so
    public static class LibclientSo {
        public const nint dwCSGOInput = 0x39751A0;
        public const nint dwEntityList = 0x37B5180;
        public const nint dwGameEntitySystem = 0x3AC9F38;
        public const nint dwGameEntitySystem_highestEntityIndex = 0x2110;
        public const nint dwGlowManager = 0x3960F10;
        public const nint dwLocalPlayerController = 0x3946FF8;
        public const nint dwLocalPlayerPawn = 0x3966DC8;
        public const nint dwPlantedC4 = 0x396CE98;
        public const nint dwPrediction = 0x3966C80;
        public const nint dwSensitivity = 0x3965248;
        public const nint dwSensitivity_sensitivity = 0x40;
        public const nint dwViewAngles = 0x397A5C8;
        public const nint dwViewRender = 0x3967830;
    }
    // Module: libengine2.so
    public static class Libengine2So {
        public const nint dwBuildNumber = 0x7AEA34;
        public const nint dwNetworkGameClient = 0x7AF3E8;
        public const nint dwNetworkGameClient_clientTickCount = 0x388;
        public const nint dwNetworkGameClient_isBackgroundMap = 0x281477;
        public const nint dwNetworkGameClient_localPlayer = 0x108;
        public const nint dwNetworkGameClient_maxClients = 0x258;
        public const nint dwNetworkGameClient_serverTickCount = 0x38C;
        public const nint dwNetworkGameClient_signOnState = 0x248;
        public const nint dwWindowHeight = 0x7B8A04;
        public const nint dwWindowWidth = 0x7B8A00;
    }
    // Module: libinputsystem.so
    public static class LibinputsystemSo {
        public const nint dwInputSystem = 0x401A0;
    }
    // Module: libmatchmaking.so
    public static class LibmatchmakingSo {
        public const nint dwGameTypes = 0x33B0A0;
        public const nint dwGameTypes_mapName = 0x33B1C0;
    }
}
