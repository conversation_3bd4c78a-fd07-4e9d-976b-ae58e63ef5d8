// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#![allow(non_upper_case_globals, non_camel_case_types, non_snake_case, unused)]

pub mod cs2_dumper {
    pub mod schemas {
        // Module: libhost.so
        // Classes count: 2
        // Enums count: 0
        pub mod libhost_so {
            // Parent: CAnimScriptBase
            // Fields count: 1
            pub mod EmptyTestScript {
                pub const m_hTest: usize = 0x10; // CAnimScriptParam<float32>
            }
            // Parent: None
            // Fields count: 1
            pub mod CAnimScriptBase {
                pub const m_bIsValid: usize = 0x8; // bool
            }
        }
    }
}
