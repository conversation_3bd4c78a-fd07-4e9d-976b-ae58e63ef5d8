// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

namespace CS2Dumper.Interfaces {
    // Module: libanimationsystem.so
    public static class LibanimationsystemSo {
        public const nint AnimationSystemUtils_001 = 0x1B4F70;
        public const nint AnimationSystem_001 = 0x1B4D60;
    }
    // Module: libclient.so
    public static class LibclientSo {
        public const nint ClientToolsInfo_001 = 0xE07340;
        public const nint EmptyWorldService001_Client = 0xACDC90;
        public const nint GameClientExports001 = 0xE06F10;
        public const nint LegacyGameUI001 = 0xFDF680;
        public const nint Source2Client002 = 0xE06F70;
        public const nint Source2ClientConfig001 = 0xAB8490;
        public const nint Source2ClientPrediction001 = 0xE96E90;
        public const nint Source2ClientUI001 = 0xF6CB90;
    }
    // Module: libengine2.so
    public static class Libengine2So {
        public const nint BenchmarkService001 = 0x2239C0;
        public const nint BugService001 = 0x21FB80;
        public const nint ClientServerEngineLoopService_001 = 0x1EEF60;
        public const nint EngineGameUI001 = 0x3BE430;
        public const nint EngineServiceMgr001 = 0x1DF0D0;
        public const nint GameEventSystemClientV001 = 0x1E4480;
        public const nint GameEventSystemServerV001 = 0x1E4490;
        public const nint GameResourceServiceClientV001 = 0x225310;
        public const nint GameResourceServiceServerV001 = 0x225320;
        public const nint GameUIService_001 = 0x22D550;
        public const nint HostStateMgr001 = 0x1E9AA0;
        public const nint INETSUPPORT_001 = 0x3879C0;
        public const nint InputService_001 = 0x231C40;
        public const nint KeyValueCache001 = 0x1EC3F0;
        public const nint MapListService_001 = 0x246C50;
        public const nint NetworkClientService_001 = 0x269BB0;
        public const nint NetworkP2PService_001 = 0x27EEF0;
        public const nint NetworkServerService_001 = 0x24AFC0;
        public const nint NetworkService_001 = 0x24A6E0;
        public const nint RenderService_001 = 0x2840D0;
        public const nint ScreenshotService001 = 0x286C50;
        public const nint SimpleEngineLoopService_001 = 0x2056D0;
        public const nint SoundService_001 = 0x28AFE0;
        public const nint Source2EngineToClient001 = 0x30C5F0;
        public const nint Source2EngineToClientStringTable001 = 0x2DF0B0;
        public const nint Source2EngineToServer001 = 0x333290;
        public const nint Source2EngineToServerStringTable001 = 0x315C80;
        public const nint SplitScreenService_001 = 0x292920;
        public const nint StatsService_001 = 0x2962E0;
        public const nint ToolService_001 = 0x29AF40;
        public const nint VENGINE_GAMEUIFUNCS_VERSION005 = 0x3BDE80;
        public const nint VProfService_001 = 0x29C7C0;
    }
    // Module: libfilesystem_stdio.so
    public static class LibfilesystemStdioSo {
        public const nint VAsyncFileSystem2_001 = 0x7DB90;
        public const nint VFileSystem017 = 0x7DB80;
    }
    // Module: libhost.so
    public static class LibhostSo {
        public const nint DebugDrawQueueManager001 = 0xC5180;
        public const nint GameModelInfo001 = 0xC0190;
        public const nint GameSystem2HostHook = 0xC08C0;
        public const nint HostUtils001 = 0xC0D40;
        public const nint PredictionDiffManager001 = 0xC1CC0;
        public const nint SaveRestoreDataVersion001 = 0xC3CC0;
        public const nint SinglePlayerSharedMemory001 = 0xC3F80;
        public const nint Source2Host001 = 0xC46E0;
    }
    // Module: libinputsystem.so
    public static class LibinputsystemSo {
        public const nint InputStackSystemVersion001 = 0x11500;
        public const nint InputSystemVersion001 = 0x129F0;
    }
    // Module: liblocalize.so
    public static class LiblocalizeSo {
        public const nint Localize_001 = 0x1D770;
    }
    // Module: libmatchmaking.so
    public static class LibmatchmakingSo {
        public const nint GameTypes001 = 0xF5D70;
        public const nint MATCHFRAMEWORK_001 = 0x1CF4C0;
    }
    // Module: libmaterialsystem2.so
    public static class Libmaterialsystem2So {
        public const nint FontManager_001 = 0x7DDF0;
        public const nint MaterialUtils_001 = 0x67C30;
        public const nint PostProcessingSystem_001 = 0x8D2C0;
        public const nint TextLayout_001 = 0x8A850;
        public const nint VMaterialSystem2_001 = 0x2C470;
    }
    // Module: libmeshsystem.so
    public static class LibmeshsystemSo {
        public const nint MeshSystem001 = 0x97630;
    }
    // Module: libnetworksystem.so
    public static class LibnetworksystemSo {
        public const nint FlattenedSerializersVersion001 = 0x163840;
        public const nint NetworkMessagesVersion001 = 0x18C1C0;
        public const nint NetworkSystemVersion001 = 0x1B5760;
        public const nint SerializedEntitiesVersion001 = 0x1CF120;
    }
    // Module: libpanorama.so
    public static class LibpanoramaSo {
        public const nint PanoramaUIEngine001 = 0x2342D0;
    }
    // Module: libpanorama_text_pango.so
    public static class LibpanoramaTextPangoSo {
        public const nint PanoramaTextServices001 = 0xBB8E0;
    }
    // Module: libpanoramauiclient.so
    public static class LibpanoramauiclientSo {
        public const nint PanoramaUIClient001 = 0x10A0D0;
    }
    // Module: libparticles.so
    public static class LibparticlesSo {
        public const nint ParticleSystemMgr003 = 0x212AF0;
    }
    // Module: libpulse_system.so
    public static class LibpulseSystemSo {
        public const nint IPulseSystem_001 = 0x402F0;
    }
    // Module: librendersystemvulkan.so
    public static class LibrendersystemvulkanSo {
        public const nint RenderDeviceMgr001 = 0x14E840;
        public const nint RenderUtils_001 = 0xCD990;
    }
    // Module: libresourcesystem.so
    public static class LibresourcesystemSo {
        public const nint ResourceSystem013 = 0x30F40;
    }
    // Module: libscenefilecache.so
    public static class LibscenefilecacheSo {
        public const nint ResponseRulesCache001 = 0x89040;
        public const nint SceneFileCache002 = 0x85BA0;
    }
    // Module: libscenesystem.so
    public static class LibscenesystemSo {
        public const nint RenderingPipelines_001 = 0x142660;
        public const nint SceneSystem_002 = 0x186F90;
        public const nint SceneUtils_001 = 0x206810;
    }
    // Module: libschemasystem.so
    public static class LibschemasystemSo {
        public const nint SchemaSystem_001 = 0x20FC0;
    }
    // Module: libserver.so
    public static class LibserverSo {
        public const nint EmptyWorldService001_Server = 0xBD2840;
        public const nint EntitySubclassUtilsV001 = 0x8B9640;
        public const nint NavGameTest001 = 0x1118F10;
        public const nint ServerToolsInfo_001 = 0xEE1440;
        public const nint Source2GameClients001 = 0xEE13D0;
        public const nint Source2GameDirector001 = 0x709580;
        public const nint Source2GameEntities001 = 0xEE13C0;
        public const nint Source2Server001 = 0xEE10C0;
        public const nint Source2ServerConfig001 = 0xB6EA60;
        public const nint customnavsystem001 = 0x83C060;
    }
    // Module: libsoundsystem.so
    public static class LibsoundsystemSo {
        public const nint SoundOpSystem001 = 0x176F30;
        public const nint SoundOpSystemEdit001 = 0xB6E30;
        public const nint SoundSystem001 = 0x1E62D0;
        public const nint VMixEditTool001 = 0x21A190;
    }
    // Module: libsteamaudio.so
    public static class LibsteamaudioSo {
        public const nint SteamAudio001 = 0x32410;
    }
    // Module: libtier0.so
    public static class Libtier0So {
        public const nint TestScriptMgr001 = 0x1B9C40;
        public const nint VEngineCvar007 = 0xF6200;
        public const nint VProcessUtils002 = 0x1AD430;
        public const nint VStringTokenSystem001 = 0x1DFD50;
    }
    // Module: libv8system.so
    public static class Libv8systemSo {
        public const nint Source2V8System001 = 0x1B450;
    }
    // Module: libvphysics2.so
    public static class Libvphysics2So {
        public const nint VPhysics2_Handle_Interface_001 = 0xC92A0;
        public const nint VPhysics2_Interface_001 = 0xC8E90;
    }
    // Module: libvscript.so
    public static class LibvscriptSo {
        public const nint VScriptManager010 = 0x25DA0;
    }
    // Module: libworldrenderer.so
    public static class LibworldrendererSo {
        public const nint WorldRendererMgr001 = 0xAED00;
    }
    // Module: steamclient.so
    public static class SteamclientSo {
        public const nint CLIENTENGINE_INTERFACE_VERSION005 = 0x1455CA0;
        public const nint IVALIDATE001 = 0x1451AC0;
        public const nint SteamClient006 = 0x111AED0;
        public const nint SteamClient007 = 0x111AEE0;
        public const nint SteamClient008 = 0x111AEF0;
        public const nint SteamClient009 = 0x111AF00;
        public const nint SteamClient010 = 0x111AF10;
        public const nint SteamClient011 = 0x111AF20;
        public const nint SteamClient012 = 0x111AF30;
        public const nint SteamClient013 = 0x111AF40;
        public const nint SteamClient014 = 0x111AF50;
        public const nint SteamClient015 = 0x111AF60;
        public const nint SteamClient016 = 0x111AF70;
        public const nint SteamClient017 = 0x111AF80;
        public const nint SteamClient018 = 0x111AF90;
        public const nint SteamClient019 = 0x111AFA0;
        public const nint SteamClient020 = 0x111AFB0;
        public const nint SteamClient021 = 0x111AFC0;
        public const nint SteamClient022 = 0x111AFD0;
        public const nint p2pvoice002 = 0x1C9B1B0;
        public const nint p2pvoicesingleton002 = 0x1C93900;
    }
}
