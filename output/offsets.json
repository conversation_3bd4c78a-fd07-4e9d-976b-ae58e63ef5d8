{"libclient.so": [{"name": "dwCSGOInput", "value": 60248480}, {"name": "dwEntityList", "value": 58413440}, {"name": "dwGameEntitySystem", "value": 61644600}, {"name": "dwGameEntitySystem_highestEntityIndex", "value": 8464}, {"name": "dwGlowManager", "value": 60165904}, {"name": "dwLocalPlayerController", "value": 60059640}, {"name": "dwLocalPlayerPawn", "value": 60190152}, {"name": "dwPlantedC4", "value": 60214936}, {"name": "dwPrediction", "value": 60189824}, {"name": "dwSensitivity", "value": 60183112}, {"name": "dwSensitivity_sensitivity", "value": 64}, {"name": "dwViewAngles", "value": 60270024}, {"name": "dwViewRender", "value": 60192816}], "libengine2.so": [{"name": "dwBuildNumber", "value": 8055348}, {"name": "dwNetworkGameClient", "value": 8057832}, {"name": "dwNetworkGameClient_clientTickCount", "value": 904}, {"name": "dwNetworkGameClient_isBackgroundMap", "value": 2626679}, {"name": "dwNetworkGameClient_localPlayer", "value": 264}, {"name": "dwNetworkGameClient_maxClients", "value": 600}, {"name": "dwNetworkGameClient_serverTickCount", "value": 908}, {"name": "dwNetworkGameClient_signOnState", "value": 584}, {"name": "dwWindowHeight", "value": 8096260}, {"name": "dwWindowWidth", "value": 8096256}], "libinputsystem.so": [{"name": "dwInputSystem", "value": 262560}], "libmatchmaking.so": [{"name": "dwGameTypes", "value": 3387552}, {"name": "dwGameTypes_mapName", "value": 3387840}]}