// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#![allow(non_upper_case_globals, unused)]

pub mod cs2_dumper {
    pub mod offsets {
        // Module: libclient.so
        pub mod libclient_so {
            pub const dwCSGOInput: usize = 0x39751A0;
            pub const dwEntityList: usize = 0x37B5180;
            pub const dwGameEntitySystem: usize = 0x3AC9F38;
            pub const dwGameEntitySystem_highestEntityIndex: usize = 0x2110;
            pub const dwGlowManager: usize = 0x3960F10;
            pub const dwLocalPlayerController: usize = 0x3946FF8;
            pub const dwLocalPlayerPawn: usize = 0x3966DC8;
            pub const dwPlantedC4: usize = 0x396CE98;
            pub const dwPrediction: usize = 0x3966C80;
            pub const dwSensitivity: usize = 0x3965248;
            pub const dwSensitivity_sensitivity: usize = 0x40;
            pub const dwViewAngles: usize = 0x397A5C8;
            pub const dwViewRender: usize = 0x3967830;
        }
        // Module: libengine2.so
        pub mod libengine2_so {
            pub const dwBuildNumber: usize = 0x7AEA34;
            pub const dwNetworkGameClient: usize = 0x7AF3E8;
            pub const dwNetworkGameClient_clientTickCount: usize = 0x388;
            pub const dwNetworkGameClient_isBackgroundMap: usize = 0x281477;
            pub const dwNetworkGameClient_localPlayer: usize = 0x108;
            pub const dwNetworkGameClient_maxClients: usize = 0x258;
            pub const dwNetworkGameClient_serverTickCount: usize = 0x38C;
            pub const dwNetworkGameClient_signOnState: usize = 0x248;
            pub const dwWindowHeight: usize = 0x7B8A04;
            pub const dwWindowWidth: usize = 0x7B8A00;
        }
        // Module: libinputsystem.so
        pub mod libinputsystem_so {
            pub const dwInputSystem: usize = 0x401A0;
        }
        // Module: libmatchmaking.so
        pub mod libmatchmaking_so {
            pub const dwGameTypes: usize = 0x33B0A0;
            pub const dwGameTypes_mapName: usize = 0x33B1C0;
        }
    }
}
