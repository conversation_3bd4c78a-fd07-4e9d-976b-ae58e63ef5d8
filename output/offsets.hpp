// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace offsets {
        // Module: libclient.so
        namespace libclient_so {
            constexpr std::ptrdiff_t dwCSGOInput = 0x39751A0;
            constexpr std::ptrdiff_t dwEntityList = 0x37B5180;
            constexpr std::ptrdiff_t dwGameEntitySystem = 0x3AC9F38;
            constexpr std::ptrdiff_t dwGameEntitySystem_highestEntityIndex = 0x2110;
            constexpr std::ptrdiff_t dwGlowManager = 0x3960F10;
            constexpr std::ptrdiff_t dwLocalPlayerController = 0x3946FF8;
            constexpr std::ptrdiff_t dwLocalPlayerPawn = 0x3966DC8;
            constexpr std::ptrdiff_t dwPlantedC4 = 0x396CE98;
            constexpr std::ptrdiff_t dwPrediction = 0x3966C80;
            constexpr std::ptrdiff_t dwSensitivity = 0x3965248;
            constexpr std::ptrdiff_t dwSensitivity_sensitivity = 0x40;
            constexpr std::ptrdiff_t dwViewAngles = 0x397A5C8;
            constexpr std::ptrdiff_t dwViewRender = 0x3967830;
        }
        // Module: libengine2.so
        namespace libengine2_so {
            constexpr std::ptrdiff_t dwBuildNumber = 0x7AEA34;
            constexpr std::ptrdiff_t dwNetworkGameClient = 0x7AF3E8;
            constexpr std::ptrdiff_t dwNetworkGameClient_clientTickCount = 0x388;
            constexpr std::ptrdiff_t dwNetworkGameClient_isBackgroundMap = 0x281477;
            constexpr std::ptrdiff_t dwNetworkGameClient_localPlayer = 0x108;
            constexpr std::ptrdiff_t dwNetworkGameClient_maxClients = 0x258;
            constexpr std::ptrdiff_t dwNetworkGameClient_serverTickCount = 0x38C;
            constexpr std::ptrdiff_t dwNetworkGameClient_signOnState = 0x248;
            constexpr std::ptrdiff_t dwWindowHeight = 0x7B8A04;
            constexpr std::ptrdiff_t dwWindowWidth = 0x7B8A00;
        }
        // Module: libinputsystem.so
        namespace libinputsystem_so {
            constexpr std::ptrdiff_t dwInputSystem = 0x401A0;
        }
        // Module: libmatchmaking.so
        namespace libmatchmaking_so {
            constexpr std::ptrdiff_t dwGameTypes = 0x33B0A0;
            constexpr std::ptrdiff_t dwGameTypes_mapName = 0x33B1C0;
        }
    }
}
