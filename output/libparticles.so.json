{"libparticles.so": {"classes": {"CBaseRendererSource2": {"fields": {"m_bAnimateInFPS": 3920, "m_bBlendFramesSeq0": 10108, "m_bDisableZBuffering": 8720, "m_bGammaCorrectVertexColors": 5660, "m_bMaxLuminanceBlendingSequence0": 10109, "m_bOnlyRenderInEffecsGameOverlay": 8459, "m_bOnlyRenderInEffectsBloomPass": 8456, "m_bOnlyRenderInEffectsWaterPass": 8457, "m_bRefract": 8096, "m_bRefractSolid": 8097, "m_bReverseZBuffering": 8719, "m_bSaturateColorPreAlphaBlend": 5661, "m_bStencilTestExclude": 8588, "m_bTintByFOW": 7048, "m_bTintByGlobalLight": 7049, "m_bUseMixedResolutionRendering": 8458, "m_bWriteStencilOnDepthFail": 8718, "m_bWriteStencilOnDepthPass": 8717, "m_flAddSelfAmount": 5664, "m_flAlphaReferenceSoftness": 7064, "m_flAlphaScale": 864, "m_flAnimationRate": 3912, "m_flBumpStrength": 3880, "m_flCenterXOffset": 3192, "m_flCenterYOffset": 3536, "m_flDepthBias": 9760, "m_flDesaturation": 6008, "m_flDiffuseAmount": 4960, "m_flDiffuseClamp": 5304, "m_flFeatheringFilter": 9416, "m_flFeatheringMaxDist": 9072, "m_flFeatheringMinDist": 8728, "m_flFogAmount": 6704, "m_flMotionVectorScaleU": 3928, "m_flMotionVectorScaleV": 4272, "m_flOverbrightFactor": 6352, "m_flRadiusScale": 520, "m_flRefractAmount": 8104, "m_flRollScale": 1208, "m_flSelfIllumAmount": 4616, "m_flSourceAlphaValueToMapToOne": 7752, "m_flSourceAlphaValueToMapToZero": 7408, "m_nAlpha2Field": 1552, "m_nAlphaReferenceType": 7060, "m_nAnimationType": 3916, "m_nColorBlendType": 3176, "m_nCropTextureOverride": 3884, "m_nFeatheringMode": 8724, "m_nFogType": 6700, "m_nHSVShiftControlPoint": 6696, "m_nLightingControlPoint": 5648, "m_nOutputBlendMode": 5656, "m_nPerParticleAlphaRefWindow": 7056, "m_nPerParticleAlphaReference": 7052, "m_nRefractBlurRadius": 8448, "m_nRefractBlurType": 8452, "m_nSelfIllumPerParticle": 5652, "m_nShaderType": 3180, "m_nSortMethod": 10104, "m_stencilTestID": 8460, "m_stencilWriteID": 8589, "m_strShaderOverride": 3184, "m_vecColorScale": 1560, "m_vecTexturesInput": 3888}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "CBaseTrailRenderer": {"fields": {"m_bClampV": 11392, "m_flEndFadeSize": 11048, "m_flMaxSize": 10700, "m_flMinSize": 10696, "m_flStartFadeSize": 10704, "m_nOrientationControlPoint": 10692, "m_nOrientationType": 10688}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "CGeneralRandomRotation": {"fields": {"m_bRandomlyFlipDirection": 464, "m_flDegrees": 448, "m_flDegreesMax": 456, "m_flDegreesMin": 452, "m_flRotationRandExponent": 460, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "CGeneralSpin": {"fields": {"m_fSpinRateStopTime": 452, "m_nSpinRateDegrees": 440, "m_nSpinRateMinDegrees": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "CNewParticleEffect": {"fields": {"m_LastMax": 140, "m_LastMin": 128, "m_RefCount": 192, "m_bAllocated": 0, "m_bAutoUpdateBBox": 0, "m_bCanFreeze": 126, "m_bDisableAggregation": 0, "m_bDontRemove": 0, "m_bForceNoDraw": 0, "m_bFreezeTargetState": 125, "m_bFreezeTransitionActive": 124, "m_bIsFirstFrame": 0, "m_bNeedsBBoxUpdate": 0, "m_bRemove": 0, "m_bShouldCheckFoW": 0, "m_bShouldPerformCullCheck": 0, "m_bShouldSave": 0, "m_bShouldSimulateDuringGamePaused": 0, "m_bSimulate": 0, "m_flFreezeTransitionDuration": 116, "m_flFreezeTransitionOverride": 120, "m_flFreezeTransitionStart": 112, "m_flScale": 76, "m_hOwner": 80, "m_nSplitScreenUser": 152, "m_pDebugName": 40, "m_pNext": 16, "m_pOwningParticleProperty": 88, "m_pParticles": 32, "m_pPrev": 24, "m_vSortOrigin": 64, "m_vecAggregationCenter": 156}, "metadata": [], "parent": "IParticleEffect"}, "CParticleCollectionBindingInstance": {"fields": {}, "metadata": [{"name": "MPulseInstanceDomainInfo", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}], "parent": "CBasePulseGraphInstance"}, "CParticleCollectionFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CParticleCollectionRendererFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleCollectionFloatInput"}, "CParticleCollectionRendererVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleCollectionVecInput"}, "CParticleCollectionVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleVecInput"}, "CParticleFloatInput": {"fields": {"m_Curve": 280, "m_NamedValue": 24, "m_bHasRandomSignFlip": 112, "m_bNoiseImgPreviewLive": 204, "m_bUseBoundsCenter": 220, "m_flBiasParameter": 272, "m_flInput0": 232, "m_flInput1": 236, "m_flLOD0": 128, "m_flLOD1": 132, "m_flLOD2": 136, "m_flLOD3": 140, "m_flLiteralValue": 20, "m_flMultFactor": 228, "m_flNoCameraFallback": 216, "m_flNoiseImgPreviewScale": 200, "m_flNoiseOffset": 172, "m_flNoiseOutputMax": 152, "m_flNoiseOutputMin": 148, "m_flNoiseScale": 156, "m_flNoiseTurbulenceMix": 196, "m_flNoiseTurbulenceScale": 192, "m_flNotchedOutputInside": 260, "m_flNotchedOutputOutside": 256, "m_flNotchedRangeMax": 252, "m_flNotchedRangeMin": 248, "m_flOutput0": 240, "m_flOutput1": 244, "m_flRandomMax": 108, "m_flRandomMin": 104, "m_nBiasType": 268, "m_nControlPoint": 88, "m_nInputMode": 224, "m_nMapType": 16, "m_nNoiseInputVectorAttribute": 144, "m_nNoiseModifier": 188, "m_nNoiseOctaves": 176, "m_nNoiseTurbulence": 180, "m_nNoiseType": 184, "m_nRandomMode": 120, "m_nRandomSeed": 116, "m_nRoundType": 264, "m_nScalarAttribute": 92, "m_nType": 12, "m_nVectorAttribute": 96, "m_nVectorComponent": 100, "m_vecNoiseOffsetRate": 160}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleFunction": {"fields": {"m_Notes": 408, "m_bDisableOperator": 406, "m_bNormalizeToStopTime": 376, "m_flOpEndFadeInTime": 360, "m_flOpEndFadeOutTime": 368, "m_flOpFadeOscillatePeriod": 372, "m_flOpStartFadeInTime": 356, "m_flOpStartFadeOutTime": 364, "m_flOpStrength": 8, "m_flOpTimeOffsetMax": 384, "m_flOpTimeOffsetMin": 380, "m_flOpTimeScaleMax": 400, "m_flOpTimeScaleMin": 396, "m_nOpEndCapState": 352, "m_nOpTimeOffsetSeed": 388, "m_nOpTimeScaleSeed": 392}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleFunctionConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionEmitter": {"fields": {"m_nEmitterIndex": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionForce": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionInitializer": {"fields": {"m_nAssociatedEmitterIndex": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionOperator": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionPreEmission": {"fields": {"m_bRunOnce": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "CParticleFunctionRenderer": {"fields": {"VisibilityInputs": 440, "m_bCannotBeRefracted": 512, "m_bSkipRenderingOnMobile": 513}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleMassCalculationParameters": {"fields": {"m_flNominalRadius": 352, "m_flRadius": 8, "m_flScale": 696, "m_nMassMode": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleModelInput": {"fields": {"m_NamedValue": 16, "m_nControlPoint": 80, "m_nType": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleProperty": {"fields": {}, "metadata": [], "parent": null}, "CParticleRemapFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CParticleSystemDefinition": {"fields": {"m_BoundingBoxMax": 552, "m_BoundingBoxMin": 540, "m_Children": 184, "m_ConstantColor": 608, "m_ConstantNormal": 612, "m_Constraints": 136, "m_Emitters": 40, "m_ForceGenerators": 112, "m_Initializers": 64, "m_NamedValueDomain": 576, "m_NamedValueLocals": 584, "m_Operators": 88, "m_PreEmissionOperators": 16, "m_Renderers": 160, "m_bEnableNamedValues": 573, "m_bInfiniteBounds": 572, "m_bScreenSpaceEffect": 788, "m_bShouldBatch": 780, "m_bShouldHitboxesFallbackToCollisionHulls": 783, "m_bShouldHitboxesFallbackToRenderBounds": 781, "m_bShouldHitboxesFallbackToSnapshot": 782, "m_bShouldSort": 808, "m_controlPointConfigurations": 880, "m_flAggregateRadius": 776, "m_flConstantLifespan": 636, "m_flConstantRadius": 624, "m_flConstantRotation": 628, "m_flConstantRotationSpeed": 632, "m_flCullFillCost": 676, "m_flCullRadius": 672, "m_flDepthSortBias": 564, "m_flMaxCreationDistance": 768, "m_flMaxDrawDistance": 760, "m_flMaximumSimTime": 732, "m_flMaximumTimeStep": 728, "m_flMinimumSimTime": 736, "m_flMinimumTimeStep": 740, "m_flNoDrawTimeToGoToSleep": 756, "m_flPreSimulationTime": 720, "m_flStartFadeDistance": 764, "m_flStopSimulationAfterTime": 724, "m_hFallback": 688, "m_hLowViolenceDef": 704, "m_hReferenceReplacement": 712, "m_hSnapshot": 656, "m_nAggregationMinAvailableParticles": 772, "m_nAllowRenderControlPoint": 804, "m_nBehaviorVersion": 8, "m_nConstantSequenceNumber": 640, "m_nConstantSequenceNumber1": 644, "m_nCullControlPoint": 680, "m_nFallbackMaxCount": 696, "m_nFirstMultipleOverride_BackwardCompat": 376, "m_nGroupID": 536, "m_nInitialParticles": 528, "m_nMaxParticles": 532, "m_nMinCPULevel": 748, "m_nMinGPULevel": 752, "m_nMinimumFrames": 744, "m_nSkipRenderControlPoint": 800, "m_nSnapshotControlPoint": 648, "m_nSortOverridePositionCP": 568, "m_nViewModelEffect": 784, "m_pszCullReplacementName": 664, "m_pszTargetLayerID": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "IParticleSystemDefinition"}, "CParticleTransformInput": {"fields": {"m_NamedValue": 16, "m_bFollowNamedValue": 80, "m_bSupportsDisabled": 81, "m_bUseOrientation": 82, "m_flEndCPGrowthTime": 92, "m_nControlPoint": 84, "m_nControlPointRangeMax": 88, "m_nType": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleVariableRef": {"fields": {"m_variableName": 0, "m_variableType": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": null}, "CParticleVecInput": {"fields": {"m_FloatComponentX": 160, "m_FloatComponentY": 504, "m_FloatComponentZ": 848, "m_FloatInterp": 1192, "m_Gradient": 1568, "m_LiteralColor": 28, "m_NamedValue": 32, "m_bFollowNamedValue": 96, "m_flInterpInput0": 1536, "m_flInterpInput1": 1540, "m_nControlPoint": 116, "m_nDeltaControlPoint": 120, "m_nType": 12, "m_nVectorAttribute": 100, "m_vCPRelativeDir": 148, "m_vCPRelativePosition": 136, "m_vCPValueScale": 124, "m_vInterpOutput0": 1544, "m_vInterpOutput1": 1556, "m_vLiteralValue": 16, "m_vRandomMax": 1604, "m_vRandomMin": 1592, "m_vVectorAttributeScale": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleVisibilityInputs": {"fields": {"m_bDotCPAngles": 44, "m_bDotCameraAngles": 45, "m_bRightEye": 68, "m_flAlphaScaleMax": 52, "m_flAlphaScaleMin": 48, "m_flCameraBias": 0, "m_flDistanceInputMax": 32, "m_flDistanceInputMin": 28, "m_flDotInputMax": 40, "m_flDotInputMin": 36, "m_flInputMax": 16, "m_flInputMin": 12, "m_flInputPixelVisFade": 20, "m_flNoPixelVisibilityFallback": 24, "m_flProxyRadius": 8, "m_flRadiusScaleFOVBase": 64, "m_flRadiusScaleMax": 60, "m_flRadiusScaleMin": 56, "m_nCPin": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPathParameters": {"fields": {"m_flBulge": 12, "m_flMidPoint": 16, "m_nBulgeControl": 8, "m_nEndControlPointNumber": 4, "m_nStartControlPointNumber": 0, "m_vEndOffset": 44, "m_vMidPointOffset": 32, "m_vStartPointOffset": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPerParticleFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CPerParticleVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleVecInput"}, "CRandomNumberGeneratorParameters": {"fields": {"m_bDistributeEvenly": 0, "m_nSeed": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CReplicationParameters": {"fields": {"m_bScaleChildParticleRadii": 4, "m_flMaxRandomRadiusScale": 352, "m_flMinRandomRadiusScale": 8, "m_flModellingScale": 3928, "m_nReplicationMode": 0, "m_vMaxRandomDisplacement": 2312, "m_vMinRandomDisplacement": 696}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSpinUpdateBase": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_INIT_AddVectorToVector": {"fields": {"m_nFieldInput": 460, "m_nFieldOutput": 456, "m_randomnessParameters": 488, "m_vOffsetMax": 476, "m_vOffsetMin": 464, "m_vecScale": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_AgeNoise": {"fields": {"m_bAbsVal": 444, "m_bAbsValInv": 445, "m_flAgeMax": 456, "m_flAgeMin": 452, "m_flNoiseScale": 460, "m_flNoiseScaleLoc": 464, "m_flOffset": 448, "m_vecOffsetLoc": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ChaoticAttractor": {"fields": {"m_bUniformSpeed": 476, "m_flAParm": 444, "m_flBParm": 448, "m_flCParm": 452, "m_flDParm": 456, "m_flScale": 460, "m_flSpeedMax": 468, "m_flSpeedMin": 464, "m_nBaseCP": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ColorLitPerParticle": {"fields": {"m_ColorMax": 472, "m_ColorMin": 468, "m_TintMax": 480, "m_TintMin": 476, "m_flLightAmplification": 492, "m_flTintPerc": 484, "m_nTintBlendMode": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateAlongPath": {"fields": {"m_PathParams": 448, "m_bSaveOffset": 528, "m_bUseRandomCPs": 512, "m_fMaxDistance": 444, "m_vEndOffset": 516}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromCPs": {"fields": {"m_nDynamicCPCount": 456, "m_nIncrement": 444, "m_nMaxCP": 452, "m_nMinCP": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromParentParticles": {"fields": {"m_bRandomDistribution": 452, "m_bSubFrame": 460, "m_flIncrement": 448, "m_flVelocityScale": 444, "m_nRandomSeed": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromPlaneCache": {"fields": {"m_bUseNormal": 469, "m_vecOffsetMax": 456, "m_vecOffsetMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateInEpitrochoid": {"fields": {"m_TransformInput": 456, "m_bOffsetExistingPos": 1930, "m_bUseCount": 1928, "m_bUseLocalCoords": 1929, "m_flOffset": 896, "m_flParticleDensity": 552, "m_flRadius1": 1240, "m_flRadius2": 1584, "m_nComponent1": 444, "m_nComponent2": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnGrid": {"fields": {"m_bCenter": 2517, "m_bHollow": 2518, "m_bLocalSpace": 2516, "m_nControlPointNumber": 2512, "m_nXCount": 448, "m_nXSpacing": 1480, "m_nYCount": 792, "m_nYSpacing": 1824, "m_nZCount": 1136, "m_nZSpacing": 2168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnModel": {"fields": {"m_HitboxSetName": 4232, "m_bEvenDistribution": 637, "m_bLocalCoords": 4360, "m_bScaleToVolume": 636, "m_bUseBones": 4361, "m_bUseMesh": 4362, "m_flBoneVelocity": 2608, "m_flMaxBoneVelocity": 2612, "m_flShellSize": 4368, "m_modelInput": 448, "m_nDesiredHitbox": 640, "m_nForceInModel": 632, "m_nHitboxValueFromControlPointIndex": 984, "m_transformInput": 536, "m_vecDirectionBias": 2616, "m_vecHitBoxScale": 992}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnModelAtHeight": {"fields": {"m_HitboxSetName": 4046, "m_bForceZ": 445, "m_bLocalCoords": 4044, "m_bPreferMovingBoxes": 4045, "m_bUseBones": 444, "m_bUseWaterHeight": 456, "m_flDesiredHeight": 464, "m_flHitboxVelocityScale": 4176, "m_flMaxBoneVelocity": 4520, "m_nBiasType": 4040, "m_nControlPointNumber": 448, "m_nHeightCP": 452, "m_vecDirectionBias": 2424, "m_vecHitBoxScale": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateParticleImpulse": {"fields": {"m_InputFalloffExp": 1144, "m_InputMagnitude": 792, "m_InputRadius": 448, "m_nFalloffFunction": 1136, "m_nImpulseType": 1488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreatePhyllotaxis": {"fields": {"m_bUseLocalCoords": 488, "m_bUseOrigRadius": 490, "m_bUseWithContEmit": 489, "m_fDistBias": 484, "m_fMinRad": 480, "m_fRadBias": 476, "m_fRadCentCore": 456, "m_fRadPerPoint": 460, "m_fRadPerPointTo": 464, "m_fpointAngle": 468, "m_fsizeOverall": 472, "m_nComponent": 452, "m_nControlPointNumber": 444, "m_nScaleCP": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSequentialPath": {"fields": {"m_PathParams": 464, "m_bCPPairs": 453, "m_bLoop": 452, "m_bSaveOffset": 454, "m_fMaxDistance": 444, "m_flNumToAssign": 448}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSequentialPathV2": {"fields": {"m_PathParams": 1152, "m_bCPPairs": 1137, "m_bLoop": 1136, "m_bSaveOffset": 1138, "m_fMaxDistance": 448, "m_flNumToAssign": 792}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSpiralSphere": {"fields": {"m_bUseParticleCount": 468, "m_flInitialRadius": 456, "m_flInitialSpeedMax": 464, "m_flInitialSpeedMin": 460, "m_nControlPointNumber": 444, "m_nDensity": 452, "m_nOverrideCP": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateWithinBox": {"fields": {"m_bLocalSpace": 3684, "m_nControlPointNumber": 3680, "m_randomnessParameters": 3688, "m_vecMax": 2064, "m_vecMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateWithinSphereTransform": {"fields": {"m_LocalCoordinateSystemSpeedMax": 5184, "m_LocalCoordinateSystemSpeedMin": 3568, "m_TransformInput": 2768, "m_bLocalCoords": 3556, "m_fRadiusMax": 792, "m_fRadiusMin": 448, "m_fSpeedMax": 3208, "m_fSpeedMin": 2864, "m_fSpeedRandExp": 3552, "m_flEndCPGrowthTime": 3560, "m_nFieldOutput": 6800, "m_nFieldVelocity": 6804, "m_vecDistanceBias": 1136, "m_vecDistanceBiasAbs": 2752}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreationNoise": {"fields": {"m_bAbsVal": 448, "m_bAbsValInv": 449, "m_flNoiseScale": 464, "m_flNoiseScaleLoc": 468, "m_flOffset": 452, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_flWorldTimeScale": 484, "m_nFieldOutput": 444, "m_vecOffsetLoc": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceCull": {"fields": {"m_bCullInside": 792, "m_flDistance": 448, "m_nControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceToCPInit": {"fields": {"m_CollisionGroupName": 1829, "m_bActiveRange": 2320, "m_bLOS": 1828, "m_flInputMax": 792, "m_flInputMin": 448, "m_flLOSScale": 2312, "m_flMaxTraceLength": 1968, "m_flOutputMax": 1480, "m_flOutputMin": 1136, "m_flRemapBias": 2336, "m_nFieldOutput": 444, "m_nSetMethod": 2316, "m_nStartCP": 1824, "m_nTraceSet": 1960, "m_vecDistanceScale": 2324}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceToNeighborCull": {"fields": {"m_bIncludeRadii": 792, "m_flDistance": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_GlobalScale": {"fields": {"m_bScalePosition": 457, "m_bScaleRadius": 456, "m_bScaleVelocity": 458, "m_flScale": 444, "m_nControlPointNumber": 452, "m_nScaleControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InheritFromParentParticles": {"fields": {"m_bRandomDistribution": 456, "m_flScale": 444, "m_nFieldOutput": 448, "m_nIncrement": 452, "m_nRandomSeed": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InheritVelocity": {"fields": {"m_flVelocityScale": 448, "m_nControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFloat": {"fields": {"m_InputStrength": 800, "m_InputValue": 448, "m_nOutputField": 792, "m_nSetMethod": 796}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFloatCollection": {"fields": {"m_InputValue": 448, "m_nOutputField": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromCPSnapshot": {"fields": {"m_bLocalSpaceAngles": 1156, "m_bRandom": 460, "m_bReverse": 461, "m_nAttributeToRead": 448, "m_nAttributeToWrite": 452, "m_nControlPointNumber": 444, "m_nLocalSpaceCP": 456, "m_nManualSnapshotIndex": 808, "m_nRandomSeed": 1152, "m_nSnapShotIncrement": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromParentKilled": {"fields": {"m_nAttributeToCopy": 444, "m_nEventType": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromVectorFieldSnapshot": {"fields": {"m_bUseVerticalVelocity": 456, "m_nControlPointNumber": 444, "m_nLocalSpaceCP": 448, "m_nWeightUpdateCP": 452, "m_vecScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitSkinnedPositionFromCPSnapshot": {"fields": {"m_bCopyAlpha": 845, "m_bCopyColor": 844, "m_bIgnoreDt": 462, "m_bRandom": 452, "m_bRigid": 460, "m_bSetNormal": 461, "m_bSetRadius": 846, "m_flBoneVelocity": 836, "m_flBoneVelocityMax": 840, "m_flIncrement": 824, "m_flMaxNormalVelocity": 468, "m_flMinNormalVelocity": 464, "m_flReadIndex": 480, "m_nControlPointNumber": 448, "m_nFullLoopIncrement": 828, "m_nIndexType": 472, "m_nRandomSeed": 456, "m_nSnapShotStartPoint": 832, "m_nSnapshotControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitVec": {"fields": {"m_InputValue": 448, "m_bNormalizedOutput": 2072, "m_bWritePreviousPosition": 2073, "m_nOutputField": 2064, "m_nSetMethod": 2068}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitVecCollection": {"fields": {"m_InputValue": 448, "m_nOutputField": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialRepulsionVelocity": {"fields": {"m_CollisionGroupName": 444, "m_bInherit": 613, "m_bPerParticle": 604, "m_bPerParticleTR": 612, "m_bProportional": 606, "m_bTranslate": 605, "m_flTraceLength": 608, "m_nChildCP": 616, "m_nChildGroupID": 620, "m_nControlPointNumber": 600, "m_nTraceSet": 572, "m_vecOutputMax": 588, "m_vecOutputMin": 576}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialSequenceFromModel": {"fields": {"m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nControlPointNumber": 444, "m_nFieldOutput": 448, "m_nFieldOutputAnim": 452, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialVelocityFromHitbox": {"fields": {"m_HitboxSetName": 456, "m_bUseBones": 584, "m_flVelocityMax": 448, "m_flVelocityMin": 444, "m_nControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialVelocityNoise": {"fields": {"m_TransformInput": 6352, "m_bIgnoreDt": 6448, "m_flNoiseScale": 5664, "m_flNoiseScaleLoc": 6008, "m_flOffset": 2088, "m_vecAbsVal": 444, "m_vecAbsValInv": 456, "m_vecOffsetLoc": 472, "m_vecOutputMax": 4048, "m_vecOutputMin": 2432}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_LifespanFromVelocity": {"fields": {"m_CollisionGroupName": 476, "m_bIncludeWater": 616, "m_flMaxTraceLength": 460, "m_flTraceOffset": 456, "m_flTraceTolerance": 464, "m_nMaxPlanes": 468, "m_nTraceSet": 604, "m_vecComponentScale": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ModelCull": {"fields": {"m_HitboxSetName": 451, "m_bBoundBox": 448, "m_bCullOutside": 449, "m_bUseBones": 450, "m_nControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_MoveBetweenPoints": {"fields": {"m_bTrailBias": 2172, "m_flEndOffset": 1824, "m_flEndSpread": 1136, "m_flSpeedMax": 792, "m_flSpeedMin": 448, "m_flStartOffset": 1480, "m_nEndControlPointNumber": 2168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_NormalAlignToCP": {"fields": {"m_nControlPointAxis": 544, "m_transformInput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_NormalOffset": {"fields": {"m_OffsetMax": 456, "m_OffsetMin": 444, "m_bLocalCoords": 472, "m_bNormalize": 473, "m_nControlPointNumber": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_OffsetVectorToVector": {"fields": {"m_nFieldInput": 444, "m_nFieldOutput": 448, "m_randomnessParameters": 476, "m_vecOutputMax": 464, "m_vecOutputMin": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_Orient2DRelToCP": {"fields": {"m_flRotOffset": 452, "m_nCP": 444, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PlaneCull": {"fields": {"m_bCullInside": 792, "m_flDistance": 448, "m_nControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PointList": {"fields": {"m_bClosedLoop": 473, "m_bPlaceAlongPath": 472, "m_nFieldOutput": 444, "m_nNumPointsAlongPath": 476, "m_pointList": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionOffset": {"fields": {"m_OffsetMax": 2064, "m_OffsetMin": 448, "m_TransformInput": 3680, "m_bLocalCoords": 3776, "m_bProportional": 3777, "m_randomnessParameters": 3780}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionOffsetToCP": {"fields": {"m_bLocalCoords": 452, "m_nControlPointNumberEnd": 448, "m_nControlPointNumberStart": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionPlaceOnGround": {"fields": {"m_CollisionGroupName": 1136, "m_bIncludeWater": 1284, "m_bOffsetonColOnly": 1288, "m_bSetNormal": 1285, "m_bSetPXYZOnly": 1286, "m_bTraceAlongNormal": 1287, "m_flMaxTraceLength": 792, "m_flOffset": 448, "m_flOffsetByRadiusFactor": 1292, "m_nIgnoreCP": 1300, "m_nPreserveOffsetCP": 1296, "m_nTraceMissBehavior": 1280, "m_nTraceSet": 1264}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionWarp": {"fields": {"m_bInvertWarp": 3704, "m_bUseCount": 3705, "m_flPrevPosScale": 3700, "m_flWarpStartTime": 3696, "m_flWarpTime": 3692, "m_nControlPointNumber": 3684, "m_nRadiusComponent": 3688, "m_nScaleControlPointNumber": 3680, "m_vecWarpMax": 2064, "m_vecWarpMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionWarpScalar": {"fields": {"m_InputValue": 472, "m_flPrevPosScale": 816, "m_nControlPointNumber": 824, "m_nScaleControlPointNumber": 820, "m_vecWarpMax": 456, "m_vecWarpMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_QuantizeFloat": {"fields": {"m_InputValue": 448, "m_nOutputField": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RadiusFromCPObject": {"fields": {"m_nControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomAlpha": {"fields": {"m_flAlphaRandExponent": 464, "m_nAlphaMax": 452, "m_nAlphaMin": 448, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomAlphaWindowThreshold": {"fields": {"m_flExponent": 452, "m_flMax": 448, "m_flMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomColor": {"fields": {"m_ColorMax": 476, "m_ColorMin": 472, "m_TintMax": 484, "m_TintMin": 480, "m_flLightAmplification": 508, "m_flTintPerc": 488, "m_flUpdateThreshold": 492, "m_nFieldOutput": 500, "m_nTintBlendMode": 504, "m_nTintCP": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomLifeTime": {"fields": {"m_fLifetimeMax": 448, "m_fLifetimeMin": 444, "m_fLifetimeRandExponent": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomModelSequence": {"fields": {"m_ActivityName": 444, "m_SequenceName": 700, "m_hModel": 960}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomNamedModelBodyPart": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomNamedModelElement": {"fields": {"m_bLinear": 481, "m_bModelFromRenderer": 482, "m_bShuffle": 480, "m_hModel": 448, "m_nFieldOutput": 484, "m_names": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomNamedModelMeshGroup": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomNamedModelSequence": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomRadius": {"fields": {"m_flRadiusMax": 448, "m_flRadiusMin": 444, "m_flRadiusRandExponent": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomRotation": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomRotationSpeed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomScalar": {"fields": {"m_flExponent": 452, "m_flMax": 448, "m_flMin": 444, "m_nFieldOutput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomSecondSequence": {"fields": {"m_nSequenceMax": 448, "m_nSequenceMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomSequence": {"fields": {"m_WeightedList": 456, "m_bLinear": 453, "m_bShuffle": 452, "m_nSequenceMax": 448, "m_nSequenceMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomTrailLength": {"fields": {"m_flLengthRandExponent": 452, "m_flMaxLength": 448, "m_flMinLength": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomVector": {"fields": {"m_nFieldOutput": 468, "m_randomnessParameters": 472, "m_vecMax": 456, "m_vecMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomVectorComponent": {"fields": {"m_flMax": 448, "m_flMin": 444, "m_nComponent": 456, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomYaw": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomYawFlip": {"fields": {"m_flPercent": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapCPtoScalar": {"fields": {"m_flEndTime": 476, "m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_flRemapBias": 484, "m_flStartTime": 472, "m_nCPInput": 444, "m_nField": 452, "m_nFieldOutput": 448, "m_nSetMethod": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialDirectionToTransformToVector": {"fields": {"m_TransformInput": 448, "m_bNormalize": 568, "m_flOffsetRot": 552, "m_flScale": 548, "m_nFieldOutput": 544, "m_vecOffsetAxis": 556}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialTransformDirectionToRotation": {"fields": {"m_TransformInput": 448, "m_flOffsetRot": 548, "m_nComponent": 552, "m_nFieldOutput": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialVisibilityScalar": {"fields": {"m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapNamedModelBodyPartToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapNamedModelElementToScalar": {"fields": {"m_bModelFromRenderer": 516, "m_hModel": 448, "m_nFieldInput": 504, "m_nFieldOutput": 508, "m_nSetMethod": 512, "m_names": 456, "m_values": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapNamedModelMeshGroupToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapNamedModelSequenceToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapParticleCountToNamedModelBodyPartScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToNamedModelElementScalar": {"fields": {"m_bModelFromRenderer": 512, "m_hModel": 488, "m_outputMaxName": 504, "m_outputMinName": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToScalar"}, "C_INIT_RemapParticleCountToNamedModelMeshGroupScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToNamedModelSequenceScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToScalar": {"fields": {"m_bActiveRange": 476, "m_bInvert": 477, "m_bWrap": 478, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_flRemapBias": 480, "m_nFieldOutput": 444, "m_nInputMax": 452, "m_nInputMin": 448, "m_nScaleControlPoint": 456, "m_nScaleControlPointField": 460, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapQAnglesToRotation": {"fields": {"m_TransformInput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapScalar": {"fields": {"m_bActiveRange": 480, "m_flEndTime": 472, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_flRemapBias": 484, "m_flStartTime": 468, "m_nFieldInput": 444, "m_nFieldOutput": 448, "m_nSetMethod": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapScalarToVector": {"fields": {"m_bLocalCoords": 500, "m_flEndTime": 488, "m_flInputMax": 456, "m_flInputMin": 452, "m_flRemapBias": 504, "m_flStartTime": 484, "m_nControlPointNumber": 496, "m_nFieldInput": 444, "m_nFieldOutput": 448, "m_nSetMethod": 492, "m_vecOutputMax": 472, "m_vecOutputMin": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapSpeedToScalar": {"fields": {"m_bPerParticle": 480, "m_flEndTime": 456, "m_flInputMax": 464, "m_flInputMin": 460, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_flStartTime": 452, "m_nControlPointNumber": 448, "m_nFieldOutput": 444, "m_nSetMethod": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapTransformOrientationToRotations": {"fields": {"m_TransformInput": 448, "m_bUseQuat": 556, "m_bWriteNormal": 557, "m_vecRotation": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapTransformToVector": {"fields": {"m_LocalSpaceTransform": 592, "m_TransformInput": 496, "m_bAccelerate": 701, "m_bOffset": 700, "m_flEndTime": 692, "m_flRemapBias": 704, "m_flStartTime": 688, "m_nFieldOutput": 444, "m_nSetMethod": 696, "m_vInputMax": 460, "m_vInputMin": 448, "m_vOutputMax": 484, "m_vOutputMin": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RingWave": {"fields": {"m_TransformInput": 448, "m_bEvenDistribution": 3296, "m_bXYVelocityOnly": 3297, "m_flInitialRadius": 888, "m_flInitialSpeedMax": 1920, "m_flInitialSpeedMin": 1576, "m_flParticlesPerOrbit": 544, "m_flPitch": 2608, "m_flRoll": 2264, "m_flThickness": 1232, "m_flYaw": 2952}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RtEnvCull": {"fields": {"m_RtEnvName": 471, "m_bCullOnMiss": 469, "m_bLifeAdjust": 470, "m_bUseVelocity": 468, "m_nComponent": 604, "m_nRTEnvCP": 600, "m_vecTestDir": 444, "m_vecTestNormal": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ScaleVelocity": {"fields": {"m_vecScale": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SequenceFromCP": {"fields": {"m_bKillUnused": 444, "m_bRadiusScale": 445, "m_nCP": 448, "m_vecOffset": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SequenceLifeTime": {"fields": {"m_flFramerate": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetAttributeToScalarExpression": {"fields": {"m_flInput1": 448, "m_flInput2": 792, "m_flOutputRemap": 1136, "m_nExpression": 444, "m_nOutputField": 1480, "m_nSetMethod": 1484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetHitboxToClosest": {"fields": {"m_HitboxSetName": 2072, "m_bUpdatePosition": 2552, "m_bUseBones": 2200, "m_bUseClosestPointOnHitbox": 2201, "m_flHybridRatio": 2208, "m_nControlPointNumber": 444, "m_nDesiredHitbox": 448, "m_nTestType": 2204, "m_vecHitBoxScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetHitboxToModel": {"fields": {"m_HitboxSetName": 2094, "m_bEvenDistribution": 452, "m_bMaintainHitbox": 2092, "m_bUseBones": 2093, "m_flShellSize": 2224, "m_nControlPointNumber": 444, "m_nDesiredHitbox": 456, "m_nForceInModel": 448, "m_vecDirectionBias": 2080, "m_vecHitBoxScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetRigidAttachment": {"fields": {"m_bLocalSpace": 456, "m_nControlPointNumber": 444, "m_nFieldInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetVectorAttributeToVectorExpression": {"fields": {"m_bNormalizedOutput": 3688, "m_nExpression": 444, "m_nOutputField": 3680, "m_nSetMethod": 3684, "m_vInput1": 448, "m_vInput2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_StatusEffect": {"fields": {"m_flAmbientScale": 472, "m_flColorWarpIntensity": 460, "m_flDetail2BlendFactor": 456, "m_flDetail2Rotation": 448, "m_flDetail2Scale": 452, "m_flDiffuseWarpBlendToFull": 464, "m_flEnvMapIntensity": 468, "m_flMetalnessBlendToFull": 508, "m_flReflectionsTintByBaseBlendToNone": 504, "m_flRimLightScale": 500, "m_flSelfIllumBlendToFull": 512, "m_flSpecularBlendToFull": 492, "m_flSpecularExponent": 484, "m_flSpecularExponentBlendToFull": 488, "m_flSpecularScale": 480, "m_nDetail2Combo": 444, "m_rimLightColor": 496, "m_specularColor": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_StatusEffectCitadel": {"fields": {"m_flSFXColorWarpAmount": 444, "m_flSFXMetalnessAmount": 452, "m_flSFXNormalAmount": 448, "m_flSFXRoughnessAmount": 456, "m_flSFXSDetailAmount": 496, "m_flSFXSDetailScale": 500, "m_flSFXSDetailScrollX": 504, "m_flSFXSDetailScrollY": 508, "m_flSFXSDetailScrollZ": 512, "m_flSFXSOffsetX": 480, "m_flSFXSOffsetY": 484, "m_flSFXSOffsetZ": 488, "m_flSFXSScale": 464, "m_flSFXSScrollX": 468, "m_flSFXSScrollY": 472, "m_flSFXSScrollZ": 476, "m_flSFXSUseModelUVs": 516, "m_flSFXSelfIllumAmount": 460, "m_nDetailCombo": 492}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityFromCP": {"fields": {"m_bDirectionOnly": 2164, "m_flVelocityScale": 2160, "m_transformInput": 2064, "m_velocityInput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityFromNormal": {"fields": {"m_bIgnoreDt": 452, "m_fSpeedMax": 448, "m_fSpeedMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityRadialRandom": {"fields": {"m_bIgnoreDelta": 1149, "m_fSpeedMax": 792, "m_fSpeedMin": 448, "m_nControlPointNumber": 444, "m_vecLocalCoordinateSystemSpeedScale": 1136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityRandom": {"fields": {"m_LocalCoordinateSystemSpeedMax": 2752, "m_LocalCoordinateSystemSpeedMin": 1136, "m_bIgnoreDT": 4368, "m_fSpeedMax": 792, "m_fSpeedMin": 448, "m_nControlPointNumber": 444, "m_randomnessParameters": 4372}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_OP_AlphaDecay": {"fields": {"m_flMinAlpha": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_AttractToControlPoint": {"fields": {"m_TransformInput": 816, "m_bApplyMinForce": 1256, "m_fFalloffPower": 808, "m_fForceAmount": 464, "m_fForceAmountMin": 912, "m_vecComponentScale": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_BasicMovement": {"fields": {"m_Gravity": 440, "m_bUseNewCode": 3444, "m_fDrag": 2056, "m_massControls": 2400, "m_nMaxConstraintPasses": 3440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_BoxConstraint": {"fields": {"m_bAccountForRadius": 3677, "m_bLocalSpace": 3676, "m_nCP": 3672, "m_vecMax": 2056, "m_vecMin": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_CPOffsetToPercentageBetweenCPs": {"fields": {"m_bRadialCheck": 472, "m_bScaleOffset": 473, "m_flInputBias": 448, "m_flInputMax": 444, "m_flInputMin": 440, "m_nEndCP": 456, "m_nInputCP": 468, "m_nOffsetCP": 460, "m_nOuputCP": 464, "m_nStartCP": 452, "m_vecOffset": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CPVelocityForce": {"fields": {"m_flScale": 456, "m_nControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_CalculateVectorAttribute": {"fields": {"m_flControlPointScale1": 488, "m_flControlPointScale2": 512, "m_flInputScale1": 456, "m_flInputScale2": 464, "m_nControlPointInput1": 468, "m_nControlPointInput2": 492, "m_nFieldInput1": 452, "m_nFieldInput2": 460, "m_nFieldOutput": 516, "m_vFinalOutputScale": 520, "m_vStartValue": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Callback": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_ChladniWave": {"fields": {"m_b3D": 5064, "m_flInputMax": 792, "m_flInputMin": 448, "m_flOutputMax": 1480, "m_flOutputMin": 1136, "m_nFieldOutput": 440, "m_nLocalSpaceControlPoint": 5060, "m_nSetMethod": 5056, "m_vecHarmonics": 3440, "m_vecWaveLength": 1824}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ChooseRandomChildrenInGroup": {"fields": {"m_flNumberOfChildren": 448, "m_nChildGroupID": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_ClampScalar": {"fields": {"m_flOutputMax": 792, "m_flOutputMin": 448, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ClampVector": {"fields": {"m_nFieldOutput": 440, "m_vecOutputMax": 2064, "m_vecOutputMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ClientPhysics": {"fields": {"m_bDeleteSim": 1234, "m_bKillParticles": 1233, "m_bRespectExclusionVolumes": 1232, "m_bStartAsleep": 528, "m_bUseHighQualitySimulation": 1224, "m_flPlayerWakeRadius": 536, "m_flVehicleWakeRadius": 880, "m_nColorBlendType": 1240, "m_nControlPoint": 1236, "m_nMaxParticleCount": 1228, "m_strPhysicsType": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_CollideWithParentParticles": {"fields": {"m_flParentRadiusScale": 440, "m_flRadiusScale": 784}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_CollideWithSelf": {"fields": {"m_flMinimumSpeed": 784, "m_flRadiusScale": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ColorAdjustHSL": {"fields": {"m_flHueAdjust": 440, "m_flLightnessAdjust": 1128, "m_flSaturationAdjust": 784}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ColorInterpolate": {"fields": {"m_ColorFade": 440, "m_bEaseInOut": 468, "m_flFadeEndTime": 460, "m_flFadeStartTime": 456, "m_nFieldOutput": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ColorInterpolateRandom": {"fields": {"m_ColorFadeMax": 468, "m_ColorFadeMin": 440, "m_bEaseInOut": 496, "m_flFadeEndTime": 488, "m_flFadeStartTime": 484, "m_nFieldOutput": 492}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ConnectParentParticleToNearest": {"fields": {"m_bUseRadius": 448, "m_flParentRadiusScale": 800, "m_flRadiusScale": 456, "m_nFirstControlPoint": 440, "m_nSecondControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ConstrainDistance": {"fields": {"m_CenterOffset": 1132, "m_bGlobalCenter": 1144, "m_fMaxDistance": 784, "m_fMinDistance": 440, "m_nControlPointNumber": 1128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainDistanceToPath": {"fields": {"m_PathParameters": 464, "m_fMinDistance": 440, "m_flMaxDistance0": 444, "m_flMaxDistance1": 452, "m_flMaxDistanceMid": 448, "m_flTravelTime": 528, "m_nFieldScale": 532, "m_nManualTField": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainDistanceToUserSpecifiedPath": {"fields": {"m_bLoopedPath": 452, "m_fMinDistance": 440, "m_flMaxDistance": 444, "m_flTimeScale": 448, "m_pointList": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainLineLength": {"fields": {"m_flMaxDistance": 444, "m_flMinDistance": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ContinuousEmitter": {"fields": {"m_bForceEmitOnFirstUpdate": 1504, "m_bForceEmitOnLastUpdate": 1505, "m_bInitFromKilledParentParticles": 1488, "m_flEmissionDuration": 448, "m_flEmissionScale": 1480, "m_flEmitRate": 1136, "m_flScalePerParentParticle": 1484, "m_flStartTime": 792, "m_nEventType": 1492, "m_nLimitPerUpdate": 1500, "m_nSnapshotControlPoint": 1496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_ControlPointToRadialScreenSpace": {"fields": {"m_nCPIn": 444, "m_nCPOut": 460, "m_nCPOutField": 464, "m_nCPSSPosOut": 468, "m_vecCP1Pos": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_ControlpointLight": {"fields": {"m_LightColor1": 1696, "m_LightColor2": 1700, "m_LightColor3": 1704, "m_LightColor4": 1708, "m_LightFiftyDist1": 1664, "m_LightFiftyDist2": 1672, "m_LightFiftyDist3": 1680, "m_LightFiftyDist4": 1688, "m_LightZeroDist1": 1668, "m_LightZeroDist2": 1676, "m_LightZeroDist3": 1684, "m_LightZeroDist4": 1692, "m_bClampLowerRange": 1726, "m_bClampUpperRange": 1727, "m_bLightDynamic1": 1716, "m_bLightDynamic2": 1717, "m_bLightDynamic3": 1718, "m_bLightDynamic4": 1719, "m_bLightType1": 1712, "m_bLightType2": 1713, "m_bLightType3": 1714, "m_bLightType4": 1715, "m_bUseHLambert": 1721, "m_bUseNormal": 1720, "m_flScale": 440, "m_nControlPoint1": 1600, "m_nControlPoint2": 1604, "m_nControlPoint3": 1608, "m_nControlPoint4": 1612, "m_vecCPOffset1": 1616, "m_vecCPOffset2": 1628, "m_vecCPOffset3": 1640, "m_vecCPOffset4": 1652}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Cull": {"fields": {"m_flCullEnd": 448, "m_flCullExp": 452, "m_flCullPerc": 440, "m_flCullStart": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CurlNoiseForce": {"fields": {"m_flWorleyJitter": 7264, "m_flWorleySeed": 6920, "m_nNoiseType": 452, "m_vecNoiseFreq": 456, "m_vecNoiseScale": 2072, "m_vecOffset": 3688, "m_vecOffsetRate": 5304}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_CycleScalar": {"fields": {"m_bDoNotRepeatCycle": 456, "m_bSynchronizeParticles": 457, "m_flCycleTime": 452, "m_flEndValue": 448, "m_flStartValue": 444, "m_nCPFieldMax": 468, "m_nCPFieldMin": 464, "m_nCPScale": 460, "m_nDestField": 440, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CylindricalDistanceToTransform": {"fields": {"m_TransformEnd": 1920, "m_TransformStart": 1824, "m_bActiveRange": 2020, "m_bAdditive": 2021, "m_bCapsule": 2022, "m_flInputMax": 792, "m_flInputMin": 448, "m_flOutputMax": 1480, "m_flOutputMin": 1136, "m_nFieldOutput": 440, "m_nSetMethod": 2016}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DampenToCP": {"fields": {"m_flRange": 444, "m_flScale": 448, "m_nControlPointNumber": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Decay": {"fields": {"m_bForcePreserveParticleOrder": 441, "m_bRopeDecay": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayClampCount": {"fields": {"m_nCount": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayMaintainCount": {"fields": {"m_bKillNewest": 800, "m_bLifespanDecay": 452, "m_flDecayDelay": 444, "m_flScale": 456, "m_nParticlesToMaintain": 440, "m_nSnapshotControlPoint": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayOffscreen": {"fields": {"m_flOffscreenTime": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DensityForce": {"fields": {"m_flForceScale": 456, "m_flRadiusScale": 452, "m_flTargetDensity": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_DifferencePreviousParticle": {"fields": {"m_bActiveRange": 468, "m_bSetPreviousParticle": 469, "m_flInputMax": 452, "m_flInputMin": 448, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_nFieldInput": 440, "m_nFieldOutput": 444, "m_nSetMethod": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Diffusion": {"fields": {"m_flRadiusScale": 440, "m_nFieldOutput": 444, "m_nVoxelGridResolution": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DirectionBetweenVecsToVec": {"fields": {"m_nFieldOutput": 440, "m_vecPoint1": 448, "m_vecPoint2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceBetweenCPsToCP": {"fields": {"m_CollisionGroupName": 489, "m_bLOS": 488, "m_bSetOnce": 460, "m_flInputMax": 468, "m_flInputMin": 464, "m_flLOSScale": 484, "m_flMaxTraceLength": 480, "m_flOutputMax": 476, "m_flOutputMin": 472, "m_nEndCP": 448, "m_nOutputCP": 452, "m_nOutputCPField": 456, "m_nSetParent": 624, "m_nStartCP": 444, "m_nTraceSet": 620}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_DistanceBetweenTransforms": {"fields": {"m_CollisionGroupName": 2024, "m_TransformEnd": 544, "m_TransformStart": 448, "m_bLOS": 2156, "m_flInputMax": 984, "m_flInputMin": 640, "m_flLOSScale": 2020, "m_flMaxTraceLength": 2016, "m_flOutputMax": 1672, "m_flOutputMin": 1328, "m_nFieldOutput": 440, "m_nSetMethod": 2160, "m_nTraceSet": 2152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceBetweenVecs": {"fields": {"m_bDeltaTime": 5060, "m_flInputMax": 4024, "m_flInputMin": 3680, "m_flOutputMax": 4712, "m_flOutputMin": 4368, "m_nFieldOutput": 440, "m_nSetMethod": 5056, "m_vecPoint1": 448, "m_vecPoint2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceCull": {"fields": {"m_bCullInside": 460, "m_flDistance": 456, "m_nControlPoint": 440, "m_vecPointOffset": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceToTransform": {"fields": {"m_CollisionGroupName": 1921, "m_TransformStart": 1824, "m_bActiveRange": 2068, "m_bAdditive": 2069, "m_bLOS": 1920, "m_flInputMax": 792, "m_flInputMin": 448, "m_flLOSScale": 2060, "m_flMaxTraceLength": 2056, "m_flOutputMax": 1480, "m_flOutputMin": 1136, "m_nFieldOutput": 440, "m_nSetMethod": 2064, "m_nTraceSet": 2052, "m_vecComponentScale": 2072}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DragRelativeToPlane": {"fields": {"m_bDirectional": 1128, "m_flDragAtPlane": 440, "m_flFalloff": 784, "m_nControlPointNumber": 2752, "m_vecPlaneNormal": 1136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DriveCPFromGlobalSoundFloat": {"fields": {"m_FieldName": 488, "m_OperatorName": 480, "m_StackName": 472, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nOutputControlPoint": 444, "m_nOutputField": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_EnableChildrenFromParentParticleCount": {"fields": {"m_bDestroyImmediately": 802, "m_bDisableChildren": 800, "m_bPlayEndcapOnStop": 801, "m_nChildGroupID": 444, "m_nFirstChild": 448, "m_nNumChildrenToEnable": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_EndCapDecay": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_EndCapTimedDecay": {"fields": {"m_flDecayTime": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_EndCapTimedFreeze": {"fields": {"m_flFreezeTime": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ExternalGameImpulseForce": {"fields": {"m_bExplosions": 802, "m_bParticles": 803, "m_bRopes": 800, "m_bRopesZOnly": 801, "m_flForceScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ExternalWindForce": {"fields": {"m_bDampenNearWaterPlane": 3690, "m_bSampleGravity": 3691, "m_bSampleWater": 3689, "m_bSampleWind": 3688, "m_bUseBasicMovementGravity": 5312, "m_flLocalBuoyancyScale": 5664, "m_flLocalGravityScale": 5320, "m_vecBuoyancyForce": 6008, "m_vecGravityForce": 3696, "m_vecSamplePosition": 456, "m_vecScale": 2072}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_FadeAndKill": {"fields": {"m_bForcePreserveParticleOrder": 464, "m_flEndAlpha": 460, "m_flEndFadeInTime": 444, "m_flEndFadeOutTime": 452, "m_flStartAlpha": 456, "m_flStartFadeInTime": 440, "m_flStartFadeOutTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeAndKillForTracers": {"fields": {"m_flEndAlpha": 460, "m_flEndFadeInTime": 444, "m_flEndFadeOutTime": 452, "m_flStartAlpha": 456, "m_flStartFadeInTime": 440, "m_flStartFadeOutTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeIn": {"fields": {"m_bProportional": 452, "m_flFadeInTimeExp": 448, "m_flFadeInTimeMax": 444, "m_flFadeInTimeMin": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeInSimple": {"fields": {"m_flFadeInTime": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeOut": {"fields": {"m_bEaseInAndOut": 513, "m_bProportional": 512, "m_flFadeBias": 452, "m_flFadeOutTimeExp": 448, "m_flFadeOutTimeMax": 444, "m_flFadeOutTimeMin": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeOutSimple": {"fields": {"m_flFadeOutTime": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ForceBasedOnDistanceToPlane": {"fields": {"m_flExponent": 500, "m_flMaxDist": 468, "m_flMinDist": 452, "m_nControlPointNumber": 496, "m_vecForceAtMaxDist": 472, "m_vecForceAtMinDist": 456, "m_vecPlaneNormal": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ForceControlPointStub": {"fields": {"m_ControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_GlobalLight": {"fields": {"m_bClampLowerRange": 444, "m_bClampUpperRange": 445, "m_flScale": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_HSVShiftToCP": {"fields": {"m_DefaultHSVColor": 456, "m_nColorCP": 444, "m_nColorGemEnableCP": 448, "m_nOutputCP": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_InheritFromParentParticles": {"fields": {"m_bRandomDistribution": 452, "m_flScale": 440, "m_nFieldOutput": 444, "m_nIncrement": 448}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InheritFromParentParticlesV2": {"fields": {"m_bRandomDistribution": 452, "m_flScale": 440, "m_nFieldOutput": 444, "m_nIncrement": 448, "m_nMissingParentBehavior": 456}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InheritFromPeerSystem": {"fields": {"m_nFieldInput": 444, "m_nFieldOutput": 440, "m_nGroupID": 452, "m_nIncrement": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InstantaneousEmitter": {"fields": {"m_flInitFromKilledParentParticles": 1136, "m_flParentParticleScale": 1144, "m_flStartTime": 792, "m_nEventType": 1140, "m_nMaxEmittedPerFrame": 1488, "m_nParticlesToEmit": 448, "m_nSnapshotControlPoint": 1492}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_InterpolateRadius": {"fields": {"m_bEaseInAndOut": 456, "m_flBias": 460, "m_flEndScale": 452, "m_flEndTime": 444, "m_flStartScale": 448, "m_flStartTime": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_IntraParticleForce": {"fields": {"m_bThreadIt": 477, "m_bUseAABB": 476, "m_flAttractionMaxDistance": 456, "m_flAttractionMaxStrength": 460, "m_flAttractionMinDistance": 452, "m_flRepulsionMaxDistance": 468, "m_flRepulsionMaxStrength": 472, "m_flRepulsionMinDistance": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_LagCompensation": {"fields": {"m_nDesiredVelocityCP": 440, "m_nDesiredVelocityCPField": 452, "m_nLatencyCP": 444, "m_nLatencyCPField": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpEndCapScalar": {"fields": {"m_flLerpTime": 448, "m_flOutput": 444, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpEndCapVector": {"fields": {"m_flLerpTime": 456, "m_nFieldOutput": 440, "m_vecOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpScalar": {"fields": {"m_flEndTime": 796, "m_flOutput": 448, "m_flStartTime": 792, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpToInitialPosition": {"fields": {"m_flInterpolation": 448, "m_flScale": 800, "m_nCacheField": 792, "m_nControlPointNumber": 440, "m_vecScale": 1144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpToOtherAttribute": {"fields": {"m_flInterpolation": 440, "m_nFieldInput": 788, "m_nFieldInputFrom": 784, "m_nFieldOutput": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpVector": {"fields": {"m_flEndTime": 460, "m_flStartTime": 456, "m_nFieldOutput": 440, "m_nSetMethod": 464, "m_vecOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LightningSnapshotGenerator": {"fields": {"m_flBranchTwist": 2864, "m_flDedicatedPool": 3904, "m_flOffset": 800, "m_flOffsetDecay": 1144, "m_flRadiusEnd": 3560, "m_flRadiusStart": 3216, "m_flRecalcRate": 1488, "m_flSegments": 456, "m_flSplitRate": 2520, "m_flUVOffset": 2176, "m_flUVScale": 1832, "m_nBranchBehavior": 3208, "m_nCPEndPnt": 452, "m_nCPSnapshot": 444, "m_nCPStartPnt": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_LocalAccelerationForce": {"fields": {"m_nCP": 452, "m_nScaleCP": 456, "m_vecAccel": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_LockPoints": {"fields": {"m_flBlendValue": 460, "m_nControlPoint": 456, "m_nMaxCol": 444, "m_nMaxRow": 452, "m_nMinCol": 440, "m_nMinRow": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToBone": {"fields": {"m_HitboxSetName": 640, "m_bRigid": 768, "m_bRigidRotationLock": 784, "m_bUseBones": 769, "m_flJumpThreshold": 632, "m_flLifeTimeFadeEnd": 628, "m_flLifeTimeFadeStart": 624, "m_flPrevPosScale": 636, "m_flRotLerp": 2408, "m_modelInput": 440, "m_nFieldOutput": 772, "m_nFieldOutputPrev": 776, "m_nRotationSetType": 780, "m_transformInput": 528, "m_vecRotation": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToPointList": {"fields": {"m_bClosedLoop": 473, "m_bPlaceAlongPath": 472, "m_nFieldOutput": 440, "m_nNumPointsAlongPath": 476, "m_pointList": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToSavedSequentialPath": {"fields": {"m_PathParams": 464, "m_bCPPairs": 452, "m_flFadeEnd": 448, "m_flFadeStart": 444}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToSavedSequentialPathV2": {"fields": {"m_PathParams": 464, "m_bCPPairs": 448, "m_flFadeEnd": 444, "m_flFadeStart": 440}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MaintainEmitter": {"fields": {"m_bEmitInstantaneously": 1152, "m_bFinalEmitOnStop": 1153, "m_flEmissionDuration": 800, "m_flEmissionRate": 1144, "m_flScale": 1160, "m_flStartTime": 792, "m_nParticlesToMaintain": 448, "m_nSnapshotControlPoint": 1148}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_MaintainSequentialPath": {"fields": {"m_PathParams": 464, "m_bLoop": 456, "m_bUseParticleCount": 457, "m_fMaxDistance": 440, "m_flCohesionStrength": 448, "m_flNumToAssign": 444, "m_flTolerance": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MaxVelocity": {"fields": {"m_flMaxVelocity": 440, "m_flMinVelocity": 444, "m_nOverrideCP": 448, "m_nOverrideCPField": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ModelCull": {"fields": {"m_HitboxSetName": 447, "m_bBoundBox": 444, "m_bCullOutside": 445, "m_bUseBones": 446, "m_nControlPointNumber": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ModelDampenMovement": {"fields": {"m_HitboxSetName": 447, "m_bBoundBox": 444, "m_bOutside": 445, "m_bUseBones": 446, "m_fDrag": 2192, "m_nControlPointNumber": 440, "m_vecPosOffset": 576}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MoveToHitbox": {"fields": {"m_HitboxSetName": 640, "m_bUseBones": 768, "m_flInterpolation": 776, "m_flLifeTimeLerpEnd": 632, "m_flLifeTimeLerpStart": 628, "m_flPrevPosScale": 636, "m_modelInput": 440, "m_nLerpType": 772, "m_transformInput": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementLoopInsideSphere": {"fields": {"m_flDistance": 448, "m_nCP": 440, "m_nDistSqrAttr": 2408, "m_vecScale": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementMaintainOffset": {"fields": {"m_bRadiusScale": 456, "m_nCP": 452, "m_vecOffset": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementMoveAlongSkinnedCPSnapshot": {"fields": {"m_bSetNormal": 448, "m_bSetRadius": 449, "m_flInterpolation": 456, "m_flTValue": 800, "m_nControlPointNumber": 440, "m_nSnapshotControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementPlaceOnGround": {"fields": {"m_CollisionGroupName": 800, "m_bIncludeShotHull": 956, "m_bIncludeWater": 957, "m_bScaleOffset": 961, "m_bSetNormal": 960, "m_flLerpRate": 796, "m_flMaxTraceLength": 784, "m_flOffset": 440, "m_flTolerance": 788, "m_flTraceOffset": 792, "m_nIgnoreCP": 968, "m_nLerpCP": 940, "m_nPreserveOffsetCP": 964, "m_nRefCP1": 932, "m_nRefCP2": 936, "m_nTraceMissBehavior": 952, "m_nTraceSet": 928}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementRigidAttachToCP": {"fields": {"m_bOffsetLocal": 460, "m_nControlPointNumber": 440, "m_nFieldInput": 452, "m_nFieldOutput": 456, "m_nScaleCPField": 448, "m_nScaleControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementRotateParticleAroundAxis": {"fields": {"m_TransformInput": 2400, "m_bLocalSpace": 2496, "m_flRotRate": 2056, "m_vecRotAxis": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementSkinnedPositionFromCPSnapshot": {"fields": {"m_bRandom": 448, "m_bSetNormal": 456, "m_bSetRadius": 457, "m_flIncrement": 808, "m_flInterpolation": 1840, "m_flReadIndex": 464, "m_nControlPointNumber": 444, "m_nFullLoopIncrement": 1152, "m_nIndexType": 460, "m_nRandomSeed": 452, "m_nSnapShotStartPoint": 1496, "m_nSnapshotControlPointNumber": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Noise": {"fields": {"m_bAdditive": 456, "m_fl4NoiseScale": 452, "m_flNoiseAnimationTimeScale": 460, "m_flOutputMax": 448, "m_flOutputMin": 444, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_NoiseEmitter": {"fields": {"m_bAbsVal": 468, "m_bAbsValInv": 469, "m_flEmissionDuration": 444, "m_flEmissionScale": 452, "m_flNoiseScale": 484, "m_flOffset": 472, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_flStartTime": 448, "m_flWorldNoiseScale": 488, "m_flWorldTimeScale": 504, "m_nScaleControlPoint": 456, "m_nScaleControlPointField": 460, "m_nWorldNoisePoint": 464, "m_vecOffsetLoc": 492}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_NormalLock": {"fields": {"m_nControlPointNumber": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_NormalizeVector": {"fields": {"m_flScale": 444, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Orient2DRelToCP": {"fields": {"m_flRotOffset": 440, "m_flSpinStrength": 444, "m_nCP": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OrientTo2dDirection": {"fields": {"m_flRotOffset": 440, "m_flSpinStrength": 444, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateScalar": {"fields": {"m_FrequencyMax": 452, "m_FrequencyMin": 448, "m_RateMax": 444, "m_RateMin": 440, "m_bProportional": 460, "m_bProportionalOp": 461, "m_flEndTime_max": 476, "m_flEndTime_min": 472, "m_flOscAdd": 484, "m_flOscMult": 480, "m_flStartTime_max": 468, "m_flStartTime_min": 464, "m_nField": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateScalarSimple": {"fields": {"m_Frequency": 444, "m_Rate": 440, "m_flOscAdd": 456, "m_flOscMult": 452, "m_nField": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateVector": {"fields": {"m_FrequencyMax": 476, "m_FrequencyMin": 464, "m_RateMax": 452, "m_RateMin": 440, "m_bOffset": 494, "m_bProportional": 492, "m_bProportionalOp": 493, "m_flEndTime_max": 508, "m_flEndTime_min": 504, "m_flOscAdd": 856, "m_flOscMult": 512, "m_flRateScale": 1200, "m_flStartTime_max": 500, "m_flStartTime_min": 496, "m_nField": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateVectorSimple": {"fields": {"m_Frequency": 452, "m_Rate": 440, "m_bOffset": 476, "m_flOscAdd": 472, "m_flOscMult": 468, "m_nField": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ParentVortices": {"fields": {"m_bFlipBasedOnYaw": 468, "m_flForceScale": 452, "m_vecTwistAxis": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_PerParticleForce": {"fields": {"m_flForceScale": 456, "m_nCP": 2416, "m_vForce": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_PercentageBetweenTransformLerpCPs": {"fields": {"m_TransformEnd": 552, "m_TransformStart": 456, "m_bActiveRange": 668, "m_bRadialCheck": 669, "m_flInputMax": 448, "m_flInputMin": 444, "m_nFieldOutput": 440, "m_nOutputEndCP": 656, "m_nOutputEndField": 660, "m_nOutputStartCP": 648, "m_nOutputStartField": 652, "m_nSetMethod": 664}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PercentageBetweenTransforms": {"fields": {"m_TransformEnd": 560, "m_TransformStart": 464, "m_bActiveRange": 660, "m_bRadialCheck": 661, "m_flInputMax": 448, "m_flInputMin": 444, "m_flOutputMax": 456, "m_flOutputMin": 452, "m_nFieldOutput": 440, "m_nSetMethod": 656}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PercentageBetweenTransformsVector": {"fields": {"m_TransformEnd": 576, "m_TransformStart": 480, "m_bActiveRange": 676, "m_bRadialCheck": 677, "m_flInputMax": 448, "m_flInputMin": 444, "m_nFieldOutput": 440, "m_nSetMethod": 672, "m_vecOutputMax": 464, "m_vecOutputMin": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PinParticleToCP": {"fields": {"m_bOffsetLocal": 2064, "m_flAge": 3112, "m_flBreakDistance": 2424, "m_flBreakSpeed": 2768, "m_flBreakValue": 3464, "m_flInterpolation": 3808, "m_nBreakControlPointNumber": 3456, "m_nBreakControlPointNumber2": 3460, "m_nControlPointNumber": 440, "m_nParticleNumber": 2072, "m_nParticleSelection": 2068, "m_nPinBreakType": 2416, "m_vecOffset": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PlanarConstraint": {"fields": {"m_PlaneNormal": 452, "m_PointOnPlane": 440, "m_bGlobalNormal": 469, "m_bGlobalOrigin": 468, "m_bUseOldCode": 1160, "m_flMaximumDistanceToCP": 816, "m_flRadiusScale": 472, "m_nControlPointNumber": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_PlaneCull": {"fields": {"m_bLocalSpace": 456, "m_flPlaneOffset": 460, "m_nPlaneControlPoint": 440, "m_vecPlaneDirection": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PlayEndCapWhenFinished": {"fields": {"m_bFireOnEmissionEnd": 441, "m_bIncludeChildren": 442}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_PointVectorAtNextParticle": {"fields": {"m_flInterpolation": 448, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PositionLock": {"fields": {"m_TransformInput": 440, "m_bLockRot": 920, "m_flEndTime_exp": 556, "m_flEndTime_max": 552, "m_flEndTime_min": 548, "m_flJumpThreshold": 912, "m_flPrevPosScale": 916, "m_flRange": 560, "m_flRangeBias": 568, "m_flStartTime_exp": 544, "m_flStartTime_max": 540, "m_flStartTime_min": 536, "m_nFieldOutput": 2544, "m_nFieldOutputPrev": 2548, "m_vecScale": 928}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_QuantizeCPComponent": {"fields": {"m_flInputValue": 448, "m_flQuantizeValue": 800, "m_nCPOutput": 792, "m_nOutVectorField": 796}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_QuantizeFloat": {"fields": {"m_InputValue": 440, "m_nOutputField": 784}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RadiusDecay": {"fields": {"m_flMinRadius": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampCPLinearRandom": {"fields": {"m_nOutControlPointNumber": 444, "m_vecRateMax": 460, "m_vecRateMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RampScalarLinear": {"fields": {"m_RateMax": 444, "m_RateMin": 440, "m_bProportionalOp": 500, "m_flEndTime_max": 460, "m_flEndTime_min": 456, "m_flStartTime_max": 452, "m_flStartTime_min": 448, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarLinearSimple": {"fields": {"m_Rate": 440, "m_flEndTime": 448, "m_flStartTime": 444, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarSpline": {"fields": {"m_RateMax": 444, "m_RateMin": 440, "m_bEaseOut": 517, "m_bProportionalOp": 516, "m_flBias": 464, "m_flEndTime_max": 460, "m_flEndTime_min": 456, "m_flStartTime_max": 452, "m_flStartTime_min": 448, "m_nField": 512}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarSplineSimple": {"fields": {"m_Rate": 440, "m_bEaseOut": 500, "m_flEndTime": 448, "m_flStartTime": 444, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RandomForce": {"fields": {"m_MaxForce": 464, "m_MinForce": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ReadFromNeighboringParticle": {"fields": {"m_DistanceCheck": 456, "m_flInterpolation": 800, "m_nFieldInput": 440, "m_nFieldOutput": 444, "m_nIncrement": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ReinitializeScalarEndCap": {"fields": {"m_flOutputMax": 448, "m_flOutputMin": 444, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapAverageHitboxSpeedtoCP": {"fields": {"m_HitboxSetName": 3464, "m_flInputMax": 808, "m_flInputMin": 464, "m_flOutputMax": 1496, "m_flOutputMin": 1152, "m_nField": 452, "m_nHeightControlPointNumber": 1840, "m_nHitboxDataType": 456, "m_nInControlPointNumber": 444, "m_nOutControlPointNumber": 448, "m_vecComparisonVelocity": 1848}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapAverageScalarValuetoCP": {"fields": {"m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nField": 452, "m_nOutControlPointNumber": 444, "m_nOutVectorField": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapBoundingVolumetoCP": {"fields": {"m_flInputMax": 452, "m_flInputMin": 448, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_nOutControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapCPVelocityToVector": {"fields": {"m_bNormalize": 452, "m_flScale": 448, "m_nControlPoint": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCPtoCP": {"fields": {"m_bDerivative": 476, "m_flInputMax": 464, "m_flInputMin": 460, "m_flInterpRate": 480, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_nInputControlPoint": 444, "m_nInputField": 452, "m_nOutputControlPoint": 448, "m_nOutputField": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapCPtoScalar": {"fields": {"m_flEndTime": 472, "m_flInputMax": 456, "m_flInputMin": 452, "m_flInterpRate": 476, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_flStartTime": 468, "m_nCPInput": 440, "m_nField": 448, "m_nFieldOutput": 444, "m_nSetMethod": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCPtoVector": {"fields": {"m_bAccelerate": 517, "m_bOffset": 516, "m_flEndTime": 504, "m_flInterpRate": 508, "m_flStartTime": 500, "m_nCPInput": 440, "m_nFieldOutput": 444, "m_nLocalSpaceCP": 448, "m_nSetMethod": 512, "m_vInputMax": 464, "m_vInputMin": 452, "m_vOutputMax": 488, "m_vOutputMin": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapControlPointDirectionToVector": {"fields": {"m_flScale": 444, "m_nControlPointNumber": 448, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapControlPointOrientationToRotation": {"fields": {"m_flOffsetRot": 448, "m_nCP": 440, "m_nComponent": 452, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCrossProductOfTwoVectorsToVector": {"fields": {"m_InputVec1": 440, "m_InputVec2": 2056, "m_bNormalize": 3676, "m_nFieldOutput": 3672}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDensityGradientToVectorAttribute": {"fields": {"m_flRadiusScale": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDensityToVector": {"fields": {"m_bUseParentDensity": 480, "m_flDensityMax": 452, "m_flDensityMin": 448, "m_flRadiusScale": 440, "m_nFieldOutput": 444, "m_nVoxelGridResolution": 484, "m_vecOutputMax": 468, "m_vecOutputMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDirectionToCPToVector": {"fields": {"m_bNormalize": 468, "m_flOffsetRot": 452, "m_flScale": 448, "m_nCP": 440, "m_nFieldOutput": 444, "m_nFieldStrength": 472, "m_vecOffsetAxis": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDistanceToLineSegmentBase": {"fields": {"m_bInfiniteLine": 456, "m_flMaxInputValue": 452, "m_flMinInputValue": 448, "m_nCP0": 440, "m_nCP1": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDistanceToLineSegmentToScalar": {"fields": {"m_flMaxOutputValue": 468, "m_flMinOutputValue": 464, "m_nFieldOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapDistanceToLineSegmentBase"}, "C_OP_RemapDistanceToLineSegmentToVector": {"fields": {"m_nFieldOutput": 460, "m_vMaxOutputValue": 476, "m_vMinOutputValue": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapDistanceToLineSegmentBase"}, "C_OP_RemapDotProductToCP": {"fields": {"m_flInputMax": 808, "m_flInputMin": 464, "m_flOutputMax": 1496, "m_flOutputMin": 1152, "m_nInputCP1": 444, "m_nInputCP2": 448, "m_nOutVectorField": 456, "m_nOutputCP": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapDotProductToScalar": {"fields": {"m_bActiveRange": 476, "m_bUseParticleNormal": 477, "m_bUseParticleVelocity": 468, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nFieldOutput": 448, "m_nInputCP1": 440, "m_nInputCP2": 444, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapExternalWindToCP": {"fields": {"m_bSetMagnitude": 2072, "m_nCP": 444, "m_nCPOutput": 448, "m_nOutVectorField": 2076, "m_vecScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapGravityToVector": {"fields": {"m_bNormalizedOutput": 2064, "m_nOutputField": 2056, "m_nSetMethod": 2060, "m_vInput1": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapModelVolumetoCP": {"fields": {"m_bBBoxOnly": 480, "m_bCubeRoot": 481, "m_flInputMax": 468, "m_flInputMin": 464, "m_flOutputMax": 476, "m_flOutputMin": 472, "m_nBBoxType": 444, "m_nField": 460, "m_nInControlPointNumber": 448, "m_nOutControlPointMaxNumber": 456, "m_nOutControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapNamedModelBodyPartEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelBodyPartOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapNamedModelElementEndCap": {"fields": {"m_bModelFromRenderer": 520, "m_fallbackNames": 496, "m_hModel": 440, "m_inNames": 448, "m_nFieldInput": 524, "m_nFieldOutput": 528, "m_outNames": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapNamedModelElementOnceTimed": {"fields": {"m_bModelFromRenderer": 520, "m_bProportional": 521, "m_fallbackNames": 496, "m_flRemapTime": 532, "m_hModel": 440, "m_inNames": 448, "m_nFieldInput": 524, "m_nFieldOutput": 528, "m_outNames": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapNamedModelMeshGroupEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelMeshGroupOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapNamedModelSequenceEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelSequenceOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapParticleCountOnScalarEndCap": {"fields": {"m_bBackwards": 460, "m_flOutputMax": 456, "m_flOutputMin": 452, "m_nFieldOutput": 440, "m_nInputMax": 448, "m_nInputMin": 444, "m_nSetMethod": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapParticleCountToScalar": {"fields": {"m_bActiveRange": 1824, "m_flOutputMax": 1480, "m_flOutputMin": 1136, "m_nFieldOutput": 440, "m_nInputMax": 792, "m_nInputMin": 448, "m_nSetMethod": 1828}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFDistanceToScalarAttribute": {"fields": {"m_flMaxDistance": 792, "m_flMinDistance": 448, "m_flValueAboveMax": 2168, "m_flValueAtMax": 1824, "m_flValueAtMin": 1480, "m_flValueBelowMin": 1136, "m_nFieldOutput": 440, "m_nVectorFieldInput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFDistanceToVectorAttribute": {"fields": {"m_flMaxDistance": 792, "m_flMinDistance": 448, "m_nVectorFieldInput": 444, "m_nVectorFieldOutput": 440, "m_vValueAboveMax": 1172, "m_vValueAtMax": 1160, "m_vValueAtMin": 1148, "m_vValueBelowMin": 1136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFGradientToVectorAttribute": {"fields": {"m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalar": {"fields": {"m_bOldCode": 464, "m_flInputMax": 452, "m_flInputMin": 448, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_nFieldInput": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalarEndCap": {"fields": {"m_flInputMax": 452, "m_flInputMin": 448, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_nFieldInput": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalarOnceTimed": {"fields": {"m_bProportional": 440, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_flRemapTime": 468, "m_nFieldInput": 444, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSpeed": {"fields": {"m_bIgnoreDelta": 464, "m_flInputMax": 448, "m_flInputMin": 444, "m_flOutputMax": 456, "m_flOutputMin": 452, "m_nFieldOutput": 440, "m_nSetMethod": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSpeedtoCP": {"fields": {"m_bUseDeltaV": 472, "m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nField": 452, "m_nInControlPointNumber": 444, "m_nOutControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapTransformOrientationToRotations": {"fields": {"m_TransformInput": 440, "m_bUseQuat": 548, "m_bWriteNormal": 549, "m_vecRotation": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformOrientationToYaw": {"fields": {"m_TransformInput": 440, "m_flRotOffset": 540, "m_flSpinStrength": 544, "m_nFieldOutput": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformToVelocity": {"fields": {"m_TransformInput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformVisibilityToScalar": {"fields": {"m_TransformInput": 448, "m_flInputMax": 552, "m_flInputMin": 548, "m_flOutputMax": 560, "m_flOutputMin": 556, "m_flRadius": 564, "m_nFieldOutput": 544, "m_nSetMethod": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformVisibilityToVector": {"fields": {"m_TransformInput": 448, "m_flInputMax": 552, "m_flInputMin": 548, "m_flRadius": 580, "m_nFieldOutput": 544, "m_nSetMethod": 440, "m_vecOutputMax": 568, "m_vecOutputMin": 556}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVectorComponentToScalar": {"fields": {"m_nComponent": 448, "m_nFieldInput": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVectortoCP": {"fields": {"m_nFieldInput": 444, "m_nOutControlPointNumber": 440, "m_nParticleNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVelocityToVector": {"fields": {"m_bNormalize": 448, "m_flScale": 444, "m_nFieldOutput": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVisibilityScalar": {"fields": {"m_flInputMax": 452, "m_flInputMin": 448, "m_flOutputMax": 460, "m_flOutputMin": 456, "m_flRadiusScale": 464, "m_nFieldInput": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RenderAsModels": {"fields": {"m_ModelList": 520, "m_bFitToModelSize": 552, "m_bNonUniformScaling": 553, "m_flModelScale": 548, "m_nSizeCullBloat": 568, "m_nXAxisScalingAttribute": 556, "m_nYAxisScalingAttribute": 560, "m_nZAxisScalingAttribute": 564}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderBlobs": {"fields": {"m_MaterialVars": 1568, "m_cubeWidth": 520, "m_cutoffRadius": 864, "m_hMaterial": 1616, "m_nIndexCountKb": 1556, "m_nScaleCP": 1560, "m_nVertexCountKb": 1552, "m_renderRadius": 1208}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderCables": {"fields": {"m_LightingTransform": 4944, "m_MaterialFloatVars": 5040, "m_MaterialVecVars": 5088, "m_bDrawCableCaps": 4912, "m_flAlphaScale": 864, "m_flCapOffsetAmount": 4920, "m_flCapRoundness": 4916, "m_flColorMapOffsetU": 3880, "m_flColorMapOffsetV": 3536, "m_flNormalMapOffsetU": 4568, "m_flNormalMapOffsetV": 4224, "m_flRadiusScale": 520, "m_flTessScale": 4924, "m_flTextureRepeatsCircumference": 3192, "m_flTextureRepeatsPerSegment": 2848, "m_hMaterial": 2832, "m_nColorBlendType": 2824, "m_nMaxTesselation": 4932, "m_nMinTesselation": 4928, "m_nRoundness": 4936, "m_nTextureRepetitionMode": 2840, "m_vecColorScale": 1208}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderClientPhysicsImpulse": {"fields": {"m_flMagnitude": 864, "m_flRadius": 520, "m_nSimIdFilter": 1208}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderClothForce": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderDeferredLight": {"fields": {"m_bUseAlphaTestWindow": 514, "m_bUseTexture": 515, "m_flAlphaScale": 520, "m_flDistanceFalloff": 2156, "m_flLightDistance": 2148, "m_flRadiusScale": 516, "m_flSpotFoV": 2160, "m_flStartFalloff": 2152, "m_hTexture": 2176, "m_nAlpha2Field": 524, "m_nAlphaTestPointField": 2164, "m_nAlphaTestRangeField": 2168, "m_nAlphaTestSharpnessField": 2172, "m_nColorBlendType": 2144, "m_nHSVShiftControlPoint": 2184, "m_vecColorScale": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderFlattenGrass": {"fields": {"m_flFlattenStrength": 516, "m_flRadiusScale": 524, "m_nStrengthFieldOverride": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderGpuImplicit": {"fields": {"m_bUsePerParticleRadius": 514, "m_fGridSize": 528, "m_fIsosurfaceThreshold": 1216, "m_fRadiusScale": 872, "m_hMaterial": 1568, "m_nIndexCountKb": 520, "m_nScaleCP": 1560, "m_nVertexCountKb": 516}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderLightBeam": {"fields": {"m_bCastShadows": 2488, "m_flBrightnessLumensPerMeter": 2144, "m_flRange": 2840, "m_flSkirt": 2496, "m_flThickness": 3184, "m_nColorBlendType": 2136, "m_vColorBlend": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderLights": {"fields": {"m_bAnimateInFPS": 536, "m_flAnimationRate": 528, "m_flEndFadeSize": 552, "m_flMaxSize": 544, "m_flMinSize": 540, "m_flStartFadeSize": 548, "m_nAnimationType": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RenderPoints"}, "C_OP_RenderMaterialProxy": {"fields": {"m_MaterialVars": 528, "m_flAlpha": 2520, "m_flMaterialOverrideEnabled": 560, "m_hOverrideMaterial": 552, "m_nColorBlendType": 2864, "m_nMaterialControlPoint": 516, "m_nProxyType": 520, "m_vecColorScale": 904}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderModels": {"fields": {"m_ActivityName": 5792, "m_ClothEffectName": 6305, "m_EconSlotName": 7196, "m_MaterialVars": 6736, "m_ModelList": 520, "m_SequenceName": 6048, "m_bAcceptsDecals": 7462, "m_bAllowApproximateTransforms": 7465, "m_bAnimated": 5424, "m_bCenterOffset": 554, "m_bDisableDepthPrepass": 7461, "m_bDisableShadows": 7460, "m_bDoNotDrawInParticlePass": 7464, "m_bEnableClothSimulation": 6304, "m_bForceDrawInterlevedWithSiblings": 7463, "m_bForceLoopingAnimation": 5777, "m_bIgnoreNormal": 552, "m_bIgnoreRadius": 3792, "m_bLocalScale": 5416, "m_bManualAnimFrame": 5779, "m_bOnlyRenderInEffecsGameOverlay": 517, "m_bOnlyRenderInEffectsBloomPass": 514, "m_bOnlyRenderInEffectsWaterPass": 515, "m_bOrientZ": 553, "m_bOriginalModel": 7452, "m_bOverrideTranslucentMaterials": 6384, "m_bResetAnimOnStop": 5778, "m_bScaleAnimationRate": 5776, "m_bSuppressTint": 7453, "m_bUseMixedResolutionRendering": 516, "m_flAlphaScale": 11912, "m_flAnimationRate": 5432, "m_flManualModelSelection": 6760, "m_flRadiusScale": 11568, "m_flRollScale": 12256, "m_hOverrideMaterial": 6376, "m_modelInput": 7104, "m_nAlpha2Field": 12600, "m_nAnimationField": 5784, "m_nAnimationScaleField": 5780, "m_nBodyGroupField": 544, "m_nColorBlendType": 14224, "m_nLOD": 7192, "m_nManualFrameField": 5788, "m_nModelScaleCP": 3796, "m_nSizeCullBloat": 5420, "m_nSkin": 6392, "m_nSubModelField": 548, "m_nSubModelFieldType": 7456, "m_szRenderAttribute": 7466, "m_vecColorScale": 12608, "m_vecComponentScale": 3800, "m_vecLocalOffset": 560, "m_vecLocalRotation": 2176}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderOmni2Light": {"fields": {"m_bCastShadows": 2832, "m_bFog": 2833, "m_bSphericalCookie": 4912, "m_flBrightnessCandelas": 2488, "m_flBrightnessLumens": 2144, "m_flFogScale": 2840, "m_flInnerConeAngle": 4216, "m_flLuminaireRadius": 3184, "m_flOuterConeAngle": 4560, "m_flRange": 3872, "m_flSkirt": 3528, "m_hLightCookie": 4904, "m_nBrightnessUnit": 2140, "m_nColorBlendType": 2136, "m_nLightType": 516, "m_vColorBlend": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderPoints": {"fields": {"m_hMaterial": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderPostProcessing": {"fields": {"m_flPostProcessStrength": 520, "m_hPostTexture": 864, "m_nPriority": 872}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderProjected": {"fields": {"m_MaterialVars": 904, "m_bEnableProjectedDepthControls": 518, "m_bFlipHorizontal": 517, "m_bOrientToNormal": 900, "m_bProjectCharacter": 514, "m_bProjectWater": 516, "m_bProjectWorld": 515, "m_flAlphaScale": 1272, "m_flAnimationTimeScale": 896, "m_flMaterialSelection": 552, "m_flMaxProjectionDepth": 524, "m_flMinProjectionDepth": 520, "m_flRadiusScale": 928, "m_flRollScale": 1616, "m_nAlpha2Field": 1960, "m_nColorBlendType": 3584, "m_vecColorScale": 1968, "m_vecProjectedMaterials": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderRopes": {"fields": {"m_bClampV": 11772, "m_bClosedLoop": 11809, "m_bDrawAsOpaque": 11820, "m_bEnableFadingAndClamping": 10688, "m_bGenerateNormals": 11821, "m_bReverseOrder": 11808, "m_bUseScalarForTextureCoordinate": 11797, "m_flEndFadeDot": 10712, "m_flEndFadeSize": 10704, "m_flMaxSize": 10696, "m_flMinSize": 10692, "m_flRadiusTaper": 10716, "m_flScalarAttributeTextureCoordScale": 11804, "m_flScaleVOffsetByControlPointDistance": 11792, "m_flScaleVScrollByControlPointDistance": 11788, "m_flScaleVSizeByControlPointDistance": 11784, "m_flStartFadeDot": 10708, "m_flStartFadeSize": 10700, "m_flTessScale": 10728, "m_flTextureVOffset": 11424, "m_flTextureVScrollRate": 11080, "m_flTextureVWorldSize": 10736, "m_nMaxTesselation": 10724, "m_nMinTesselation": 10720, "m_nOrientationType": 11812, "m_nScalarFieldForTextureCoordinate": 11800, "m_nScaleCP1": 11776, "m_nScaleCP2": 11780, "m_nTextureVParamsCP": 11768, "m_nVectorFieldForOrientation": 11816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "C_OP_RenderScreenShake": {"fields": {"m_flAmplitudeScale": 528, "m_flDurationScale": 516, "m_flFrequencyScale": 524, "m_flRadiusScale": 520, "m_nAmplitudeField": 544, "m_nDurationField": 536, "m_nFilterCP": 548, "m_nFrequencyField": 540, "m_nRadiusField": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderScreenVelocityRotate": {"fields": {"m_flForwardDegrees": 520, "m_flRotateRateDegrees": 516}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSimpleModelCollection": {"fields": {"m_bAcceptsDecals": 962, "m_bCenterOffset": 514, "m_bDisableMotionBlur": 961, "m_bDisableShadows": 960, "m_fSizeCullScale": 616, "m_hModel": 520, "m_modelInput": 528, "m_nAngularVelocityField": 964}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSound": {"fields": {"m_bSuppressStopSoundEvent": 812, "m_flDurationScale": 516, "m_flPitchScale": 524, "m_flSndLvlScale": 520, "m_flVolumeScale": 528, "m_nCPReference": 552, "m_nChannel": 548, "m_nDurationField": 536, "m_nPitchField": 540, "m_nSndLvlField": 532, "m_nVolumeField": 544, "m_pszSoundName": 556}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSprites": {"fields": {"m_OutlineColor": 12789, "m_bDistanceAlpha": 12776, "m_bOutline": 12788, "m_bParticleShadows": 13512, "m_bSoftEdges": 12777, "m_bUseYawWithNormalAligned": 11040, "m_flAlphaAdjustWithSizeAdjust": 11736, "m_flEdgeSoftnessEnd": 12784, "m_flEdgeSoftnessStart": 12780, "m_flEndFadeDot": 12772, "m_flEndFadeSize": 12424, "m_flLightingDirectionality": 13168, "m_flLightingTessellation": 12824, "m_flMaxSize": 11392, "m_flMinSize": 11048, "m_flOutlineEnd0": 12808, "m_flOutlineEnd1": 12812, "m_flOutlineStart0": 12800, "m_flOutlineStart1": 12804, "m_flShadowDensity": 13516, "m_flStartFadeDot": 12768, "m_flStartFadeSize": 12080, "m_nLightingMode": 12816, "m_nOrientationControlPoint": 11036, "m_nOrientationType": 11032, "m_nOutlineAlpha": 12796, "m_nSequenceOverride": 10688, "m_replicationParameters": 13520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "C_OP_RenderStandardLight": {"fields": {"m_bCastShadows": 2488, "m_bClosedLoop": 4945, "m_bIgnoreDT": 4960, "m_bRenderDiffuse": 4568, "m_bRenderSpecular": 4569, "m_bReverseOrder": 4944, "m_flCapsuleLength": 4940, "m_flConstrainRadiusToLengthRatio": 4964, "m_flFalloffLinearity": 3536, "m_flFiftyPercentFalloff": 3880, "m_flFogContribution": 4592, "m_flIntensity": 2144, "m_flLengthFadeInTime": 4972, "m_flLengthScale": 4968, "m_flMaxLength": 4952, "m_flMinLength": 4956, "m_flPhi": 2840, "m_flRadiusMultiplier": 3184, "m_flTheta": 2496, "m_flZeroPercentFalloff": 4224, "m_lightCookie": 4576, "m_nAttenuationStyle": 3528, "m_nCapsuleLightBehavior": 4936, "m_nColorBlendType": 2136, "m_nFogLightingMode": 4588, "m_nLightType": 516, "m_nPrevPntSource": 4948, "m_nPriority": 4584, "m_vecColorScale": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderStatusEffect": {"fields": {"m_pTextureColorWarp": 520, "m_pTextureDetail2": 528, "m_pTextureDiffuseWarp": 536, "m_pTextureEnvMap": 568, "m_pTextureFresnelColorWarp": 544, "m_pTextureFresnelWarp": 552, "m_pTextureSpecularWarp": 560}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderStatusEffectCitadel": {"fields": {"m_pTextureColorWarp": 520, "m_pTextureDetail": 560, "m_pTextureMetalness": 536, "m_pTextureNormal": 528, "m_pTextureRoughness": 544, "m_pTextureSelfIllum": 552}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderText": {"fields": {"m_DefaultText": 520, "m_OutlineColor": 514}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderTonemapController": {"fields": {"m_flTonemapLevel": 516, "m_flTonemapWeight": 520, "m_nTonemapLevelField": 524, "m_nTonemapWeightField": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderTrails": {"fields": {"m_bEnableFadingAndClamping": 11393, "m_bFlipUVBasedOnPitchYaw": 16052, "m_bIgnoreDT": 11416, "m_flConstrainRadiusToLengthRatio": 11420, "m_flEndFadeDot": 11400, "m_flForwardShift": 16048, "m_flHeadAlphaScale": 13392, "m_flLengthFadeInTime": 11428, "m_flLengthScale": 11424, "m_flMaxLength": 11408, "m_flMinLength": 11412, "m_flRadiusHeadTaper": 11432, "m_flRadiusTaper": 13736, "m_flStartFadeDot": 11396, "m_flTailAlphaScale": 15696, "m_nHorizCropField": 16040, "m_nPrevPntSource": 11404, "m_nVertCropField": 16044, "m_vecHeadColorScale": 11776, "m_vecTailColorScale": 14080}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseTrailRenderer"}, "C_OP_RenderTreeShake": {"fields": {"m_flControlPointOrientationAmount": 548, "m_flPeakStrength": 516, "m_flRadialAmount": 544, "m_flRadius": 524, "m_flShakeDuration": 532, "m_flTransitionTime": 536, "m_flTwistAmount": 540, "m_nControlPointForLinearDirection": 552, "m_nPeakStrengthFieldOverride": 520, "m_nRadiusFieldOverride": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderVRHapticEvent": {"fields": {"m_flAmplitude": 528, "m_nHand": 516, "m_nOutputField": 524, "m_nOutputHandCP": 520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RepeatedTriggerChildGroup": {"fields": {"m_bLimitChildCount": 1480, "m_flClusterCooldown": 1136, "m_flClusterRefireTime": 448, "m_flClusterSize": 792, "m_nChildGroupID": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RestartAfterDuration": {"fields": {"m_bOnlyChildren": 460, "m_flDurationMax": 444, "m_flDurationMin": 440, "m_nCP": 448, "m_nCPField": 452, "m_nChildGroupID": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RopeSpringConstraint": {"fields": {"m_flAdjustmentScale": 1472, "m_flInitialRestingLength": 1480, "m_flMaxDistance": 1128, "m_flMinDistance": 784, "m_flRestLength": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_RotateVector": {"fields": {"m_bNormalize": 476, "m_flRotRateMax": 472, "m_flRotRateMin": 468, "m_flScale": 480, "m_nFieldOutput": 440, "m_vecRotAxisMax": 456, "m_vecRotAxisMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RtEnvCull": {"fields": {"m_RtEnvName": 466, "m_bCullOnMiss": 464, "m_bStickInsteadOfCull": 465, "m_nComponent": 600, "m_nRTEnvCP": 596, "m_vecTestDir": 440, "m_vecTestNormal": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SDFConstraint": {"fields": {"m_flMaxDist": 784, "m_flMinDist": 440, "m_nMaxIterations": 1128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_SDFForce": {"fields": {"m_flForceScale": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_SDFLighting": {"fields": {"m_vLightingDir": 440, "m_vTint_0": 452, "m_vTint_1": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SelectivelyEnableChildren": {"fields": {"m_bDestroyImmediately": 1481, "m_bPlayEndcapOnStop": 1480, "m_nChildGroupID": 448, "m_nFirstChild": 792, "m_nNumChildrenToEnable": 1136}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SequenceFromModel": {"fields": {"m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nControlPointNumber": 440, "m_nFieldOutput": 444, "m_nFieldOutputAnim": 448, "m_nSetMethod": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetAttributeToScalarExpression": {"fields": {"m_flInput1": 448, "m_flInput2": 792, "m_flOutputRemap": 1136, "m_nExpression": 440, "m_nOutputField": 1480, "m_nSetMethod": 1484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToDirection": {"fields": {"m_nInputControlPoint": 440, "m_nOutputControlPoint": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToGroundNormal": {"fields": {"m_CollisionGroupName": 456, "m_bIncludeWater": 608, "m_flInterpRate": 440, "m_flMaxTraceLength": 444, "m_flTolerance": 448, "m_flTraceOffset": 452, "m_nInputCP": 588, "m_nOutputCP": 592, "m_nTraceSet": 584}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToPointAtCP": {"fields": {"m_b2DOrientation": 800, "m_bAvoidSingularity": 801, "m_bPointAway": 802, "m_flInterpolation": 456, "m_nInputCP": 444, "m_nOutputCP": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetCPtoVector": {"fields": {"m_nCPInput": 440, "m_nFieldOutput": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetChildControlPoints": {"fields": {"m_bReverse": 800, "m_bSetOrientation": 801, "m_nChildGroupID": 440, "m_nFirstControlPoint": 444, "m_nFirstSourcePoint": 456, "m_nNumControlPoints": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetControlPointFieldFromVectorExpression": {"fields": {"m_flOutputRemap": 3680, "m_nExpression": 444, "m_nOutVectorField": 4028, "m_nOutputCP": 4024, "m_vecInput1": 448, "m_vecInput2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFieldToScalarExpression": {"fields": {"m_flInput1": 448, "m_flInput2": 792, "m_flOutputRemap": 1136, "m_nExpression": 444, "m_nOutVectorField": 1484, "m_nOutputCP": 1480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFieldToWater": {"fields": {"m_nCPField": 452, "m_nDestCP": 448, "m_nSourceCP": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFromObjectScale": {"fields": {"m_nCPInput": 444, "m_nCPOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointOrientation": {"fields": {"m_bRandomize": 443, "m_bSetOnce": 444, "m_bUseWorldLocation": 441, "m_flInterpolation": 480, "m_nCP": 448, "m_nHeadLocation": 452, "m_vecRotation": 456, "m_vecRotationB": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointOrientationToCPVelocity": {"fields": {"m_nCPInput": 444, "m_nCPOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositionToRandomActiveCP": {"fields": {"m_flResetRate": 456, "m_nCP1": 444, "m_nHeadLocationMax": 452, "m_nHeadLocationMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositionToTimeOfDayValue": {"fields": {"m_nControlPointNumber": 444, "m_pszTimeOfDayParameter": 448, "m_vecDefaultValue": 576}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositions": {"fields": {"m_bOrient": 442, "m_bSetOnce": 443, "m_bUseWorldLocation": 441, "m_nCP1": 444, "m_nCP2": 448, "m_nCP3": 452, "m_nCP4": 456, "m_nHeadLocation": 508, "m_vecCP1Pos": 460, "m_vecCP2Pos": 472, "m_vecCP3Pos": 484, "m_vecCP4Pos": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointRotation": {"fields": {"m_flRotRate": 2064, "m_nCP": 2408, "m_nLocalCP": 2412, "m_vecRotAxis": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToCPVelocity": {"fields": {"m_bNormalize": 452, "m_nCPField": 460, "m_nCPInput": 444, "m_nCPOutputMag": 456, "m_nCPOutputVel": 448, "m_vecComparisonVelocity": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToCenter": {"fields": {"m_bUseAvgParticlePos": 460, "m_nCP1": 444, "m_nSetParent": 464, "m_vecCP1Pos": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToHMD": {"fields": {"m_bOrientToHMD": 460, "m_nCP1": 444, "m_vecCP1Pos": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToHand": {"fields": {"m_bOrientToHand": 464, "m_nCP1": 444, "m_nHand": 448, "m_vecCP1Pos": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToImpactPoint": {"fields": {"m_CollisionGroupName": 820, "m_bIncludeWater": 954, "m_bSetToEndpoint": 952, "m_bTraceToClosestSurface": 953, "m_flOffset": 804, "m_flStartOffset": 800, "m_flTraceLength": 456, "m_flUpdateRate": 452, "m_nCPIn": 448, "m_nCPOut": 444, "m_nTraceSet": 948, "m_vecTraceDir": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToPlayer": {"fields": {"m_bOrientToEyes": 460, "m_nCP1": 444, "m_vecCP1Pos": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToVectorExpression": {"fields": {"m_bNormalizedOutput": 3688, "m_nExpression": 444, "m_nOutputCP": 448, "m_vInput1": 456, "m_vInput2": 2072}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToWaterSurface": {"fields": {"m_bAdaptiveThreshold": 808, "m_flRetestRate": 464, "m_nActiveCP": 456, "m_nActiveCPField": 460, "m_nDestCP": 448, "m_nFlowCP": 452, "m_nSourceCP": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointsToModelParticles": {"fields": {"m_AttachmentName": 568, "m_HitboxSetName": 440, "m_bAttachment": 709, "m_bSkin": 708, "m_nFirstControlPoint": 696, "m_nFirstSourcePoint": 704, "m_nNumControlPoints": 700}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetControlPointsToParticle": {"fields": {"m_bSetOrientation": 456, "m_nChildGroupID": 440, "m_nFirstControlPoint": 444, "m_nFirstSourcePoint": 452, "m_nNumControlPoints": 448, "m_nOrientationMode": 460, "m_nSetParent": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloat": {"fields": {"m_InputValue": 440, "m_Lerp": 792, "m_nOutputField": 784, "m_nSetMethod": 788}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloatAttributeToVectorExpression": {"fields": {"m_flOutputRemap": 3680, "m_nExpression": 440, "m_nOutputField": 4024, "m_nSetMethod": 4028, "m_vInput1": 448, "m_vInput2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloatCollection": {"fields": {"m_InputValue": 440, "m_Lerp": 792, "m_nOutputField": 784, "m_nSetMethod": 788}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFromCPSnapshot": {"fields": {"m_bPrev": 1497, "m_bRandom": 456, "m_bReverse": 457, "m_bSubSample": 1496, "m_flInterpolation": 1152, "m_nAttributeToRead": 444, "m_nAttributeToWrite": 448, "m_nControlPointNumber": 440, "m_nLocalSpaceCP": 452, "m_nRandomSeed": 460, "m_nSnapShotIncrement": 808, "m_nSnapShotStartPoint": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetGravityToCP": {"fields": {"m_bSetOrientation": 800, "m_bSetZDown": 801, "m_flScale": 456, "m_nCPInput": 444, "m_nCPOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetParentControlPointsToChildCP": {"fields": {"m_bSetOrientation": 460, "m_nChildControlPoint": 448, "m_nChildGroupID": 444, "m_nFirstSourcePoint": 456, "m_nNumControlPoints": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetPerChildControlPoint": {"fields": {"m_bNumBasedOnParticleCount": 1152, "m_bSetOrientation": 1144, "m_nChildGroupID": 440, "m_nFirstControlPoint": 444, "m_nFirstSourcePoint": 800, "m_nNumControlPoints": 448, "m_nOrientationField": 1148, "m_nParticleIncrement": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetPerChildControlPointFromAttribute": {"fields": {"m_bNumBasedOnParticleCount": 460, "m_nAttributeToRead": 464, "m_nCPField": 468, "m_nChildGroupID": 440, "m_nFirstControlPoint": 444, "m_nFirstSourcePoint": 456, "m_nNumControlPoints": 448, "m_nParticleIncrement": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetRandomControlPointPosition": {"fields": {"m_bOrient": 442, "m_bUseWorldLocation": 441, "m_flInterpolation": 824, "m_flReRandomRate": 456, "m_nCP1": 444, "m_nHeadLocation": 448, "m_vecCPMaxPos": 812, "m_vecCPMinPos": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetSimulationRate": {"fields": {"m_flSimulationScale": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetSingleControlPointPosition": {"fields": {"m_bSetOnce": 441, "m_nCP1": 444, "m_transformInput": 2064, "m_vecCP1Pos": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetToCP": {"fields": {"m_bOffsetLocal": 456, "m_nControlPointNumber": 440, "m_vecOffset": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetUserEvent": {"fields": {"m_flFallingEdge": 1136, "m_flInput": 440, "m_flRisingEdge": 784, "m_nFallingEventType": 1480, "m_nRisingEventType": 1128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetVariable": {"fields": {"m_floatInput": 2248, "m_positionOffset": 608, "m_rotationOffset": 620, "m_transformInput": 512, "m_variableReference": 448, "m_vecInput": 632}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetVec": {"fields": {"m_InputValue": 440, "m_Lerp": 2064, "m_bNormalizedOutput": 2408, "m_nOutputField": 2056, "m_nSetMethod": 2060}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetVectorAttributeToVectorExpression": {"fields": {"m_bNormalizedOutput": 3688, "m_nExpression": 440, "m_nOutputField": 3680, "m_nSetMethod": 3684, "m_vInput1": 448, "m_vInput2": 2064}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ShapeMatchingConstraint": {"fields": {"m_flShapeRestorationTime": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_SnapshotRigidSkinToBones": {"fields": {"m_bTransformNormals": 440, "m_bTransformRadii": 441, "m_nControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SnapshotSkinToBones": {"fields": {"m_bTransformNormals": 440, "m_bTransformRadii": 441, "m_flJumpThreshold": 456, "m_flLifeTimeFadeEnd": 452, "m_flLifeTimeFadeStart": 448, "m_flPrevPosScale": 460, "m_nControlPointNumber": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Spin": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralSpin"}, "C_OP_SpinUpdate": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CSpinUpdateBase"}, "C_OP_SpinYaw": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralSpin"}, "C_OP_SpringToVectorConstraint": {"fields": {"m_flMaxDistance": 1128, "m_flMinDistance": 784, "m_flRestLength": 440, "m_flRestingLength": 1472, "m_vecAnchorVector": 1816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_StopAfterCPDuration": {"fields": {"m_bDestroyImmediately": 792, "m_bPlayEndCap": 793, "m_flDuration": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_TeleportBeam": {"fields": {"m_flAlpha": 488, "m_flArcMaxDuration": 476, "m_flArcSpeed": 484, "m_flSegmentBreak": 480, "m_nCPColor": 452, "m_nCPExtraArcData": 460, "m_nCPInvalidColor": 456, "m_nCPMisc": 448, "m_nCPPosition": 440, "m_nCPVelocity": 444, "m_vGravity": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_TimeVaryingForce": {"fields": {"m_EndingForce": 472, "m_StartingForce": 456, "m_flEndLerpTime": 468, "m_flStartLerpTime": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_TurbulenceForce": {"fields": {"m_flNoiseCoordScale0": 452, "m_flNoiseCoordScale1": 456, "m_flNoiseCoordScale2": 460, "m_flNoiseCoordScale3": 464, "m_vecNoiseAmount0": 468, "m_vecNoiseAmount1": 480, "m_vecNoiseAmount2": 492, "m_vecNoiseAmount3": 504}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_TwistAroundAxis": {"fields": {"m_TwistAxis": 456, "m_bLocalSpace": 468, "m_fForceAmount": 452, "m_nControlPointNumber": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_UpdateLightSource": {"fields": {"m_flBrightnessScale": 444, "m_flMaximumLightingRadius": 456, "m_flMinimumLightingRadius": 452, "m_flPositionDampingConstant": 460, "m_flRadiusScale": 448, "m_vColorTint": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VectorFieldSnapshot": {"fields": {"m_bLockToSurface": 2421, "m_bSetVelocity": 2420, "m_flBoundaryDampening": 2416, "m_flGridSpacing": 2424, "m_flInterpolation": 456, "m_nAttributeToWrite": 444, "m_nControlPointNumber": 440, "m_nLocalSpaceCP": 448, "m_vecScale": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VectorNoise": {"fields": {"m_bAdditive": 472, "m_bOffset": 473, "m_fl4NoiseScale": 468, "m_flNoiseAnimationTimeScale": 476, "m_nFieldOutput": 440, "m_vecOutputMax": 456, "m_vecOutputMin": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VelocityDecay": {"fields": {"m_flMinVelocity": 440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VelocityMatchingForce": {"fields": {"m_bUseAABB": 456, "m_flDirScale": 440, "m_flFacingStrength": 452, "m_flNeighborDistance": 448, "m_flSpdScale": 444, "m_nCPBroadcast": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_WindForce": {"fields": {"m_vForce": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_WorldCollideConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_WorldTraceConstraint": {"fields": {"m_CollisionGroupName": 468, "m_bBrushOnly": 597, "m_bDecayBounce": 2000, "m_bIncludeWater": 598, "m_bKillonContact": 2001, "m_bSetNormal": 2008, "m_bWorldOnly": 596, "m_flBounceAmount": 968, "m_flCollisionConfirmationSpeed": 616, "m_flCpMovementTolerance": 604, "m_flMinSpeed": 2004, "m_flRadiusScale": 624, "m_flRandomDirScale": 1656, "m_flRetestRate": 608, "m_flSlideAmount": 1312, "m_flStopSpeed": 2016, "m_flTraceTolerance": 612, "m_nCP": 440, "m_nCollisionMode": 456, "m_nCollisionModeMin": 460, "m_nEntityStickDataField": 2360, "m_nEntityStickNormalField": 2364, "m_nIgnoreCP": 600, "m_nMaxTracesPerFrame": 620, "m_nStickOnCollisionField": 2012, "m_nTraceSet": 464, "m_vecCpOffset": 444}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "CollisionGroupContext_t": {"fields": {"m_nCollisionGroupNumber": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ControlPointReference_t": {"fields": {"m_bOffsetInLocalSpace": 16, "m_controlPointNameString": 0, "m_vOffsetFromControlPoint": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FloatInputMaterialVariable_t": {"fields": {"m_flInput": 8, "m_strVariable": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "IParticleCollection": {"fields": {}, "metadata": [], "parent": null}, "IParticleEffect": {"fields": {}, "metadata": [], "parent": null}, "IParticleSystemDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MaterialVariable_t": {"fields": {"m_flScale": 12, "m_nVariableField": 8, "m_strVariable": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ModelReference_t": {"fields": {"m_flRelativeProbabilityOfSpawn": 8, "m_model": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PARTICLE_EHANDLE__": {"fields": {"unused": 0}, "metadata": [], "parent": null}, "PARTICLE_WORLD_HANDLE__": {"fields": {"unused": 0}, "metadata": [], "parent": null}, "ParticleAttributeIndex_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "ParticleChildrenInfo_t": {"fields": {"m_ChildRef": 0, "m_bDisableChild": 13, "m_bEndCap": 12, "m_flDelay": 8, "m_nDetailLevel": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleControlPointConfiguration_t": {"fields": {"m_drivers": 8, "m_name": 0, "m_previewState": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleControlPointDriver_t": {"fields": {"m_angOffset": 28, "m_attachmentName": 8, "m_entityName": 40, "m_iAttachType": 4, "m_iControlPoint": 0, "m_vecOffset": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleNamedValueConfiguration_t": {"fields": {"m_BoundEntityPath": 32, "m_ConfigName": 0, "m_ConfigValue": 8, "m_iAttachType": 24, "m_strAttachmentName": 48, "m_strEntityScope": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleNamedValueSource_t": {"fields": {"m_DefaultConfig": 16, "m_IsPublic": 8, "m_Name": 0, "m_NamedConfigs": 72, "m_ValueType": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticlePreviewBodyGroup_t": {"fields": {"m_bodyGroupName": 0, "m_nValue": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticlePreviewState_t": {"fields": {"m_bAnimationNonLooping": 84, "m_bShouldDrawAttachmentNames": 82, "m_bShouldDrawAttachments": 81, "m_bShouldDrawControlPointAxes": 83, "m_bShouldDrawHitboxes": 80, "m_flParticleSimulationRate": 76, "m_flPlaybackSpeed": 72, "m_groundType": 12, "m_hitboxSetName": 32, "m_materialGroupName": 40, "m_nFireParticleOnSequenceFrame": 24, "m_nModSpecificData": 8, "m_previewModel": 0, "m_sequenceName": 16, "m_vecBodyGroups": 48, "m_vecPreviewGravity": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PointDefinitionWithTimeValues_t": {"fields": {"m_flTimeDuration": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "PointDefinition_t"}, "PointDefinition_t": {"fields": {"m_bLocalCoords": 4, "m_nControlPoint": 0, "m_vOffset": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "RenderProjectedMaterial_t": {"fields": {"m_hMaterial": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SequenceWeightedList_t": {"fields": {"m_flRelativeWeight": 4, "m_nSequence": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TextureControls_t": {"fields": {"m_bClampUVs": 2409, "m_bRandomizeOffsets": 2408, "m_flDistortion": 2064, "m_flFinalTextureOffsetU": 688, "m_flFinalTextureOffsetV": 1032, "m_flFinalTextureScaleU": 0, "m_flFinalTextureScaleV": 344, "m_flFinalTextureUVRotation": 1376, "m_flZoomScale": 1720, "m_nPerParticleBlend": 2412, "m_nPerParticleDistortion": 2436, "m_nPerParticleOffsetU": 2420, "m_nPerParticleOffsetV": 2424, "m_nPerParticleRotation": 2428, "m_nPerParticleScale": 2416, "m_nPerParticleZoom": 2432}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TextureGroup_t": {"fields": {"m_Gradient": 16, "m_TextureControls": 400, "m_bEnabled": 0, "m_bReplaceTextureWithGradient": 1, "m_flTextureBlend": 56, "m_hTexture": 8, "m_nTextureBlendMode": 48, "m_nTextureChannels": 44, "m_nTextureType": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VecInputMaterialVariable_t": {"fields": {"m_strVariable": 0, "m_vecInput": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}}, "enums": {"AnimationType_t": {"alignment": 4, "members": {"ANIMATION_TYPE_FIT_LIFETIME": 1, "ANIMATION_TYPE_FIXED_RATE": 0, "ANIMATION_TYPE_MANUAL_FRAMES": 2}, "type": "uint32"}, "BBoxVolumeType_t": {"alignment": 4, "members": {"BBOX_DIMENSIONS": 1, "BBOX_MINS_MAXS": 2, "BBOX_VOLUME": 0}, "type": "uint32"}, "BlurFilterType_t": {"alignment": 4, "members": {"BLURFILTER_BOX": 1, "BLURFILTER_GAUSSIAN": 0}, "type": "uint32"}, "ClosestPointTestType_t": {"alignment": 4, "members": {"PARTICLE_CLOSEST_TYPE_BOX": 0, "PARTICLE_CLOSEST_TYPE_CAPSULE": 1, "PARTICLE_CLOSEST_TYPE_HYBRID": 2}, "type": "uint32"}, "Detail2Combo_t": {"alignment": 4, "members": {"DETAIL_2_COMBO_ADD": 1, "DETAIL_2_COMBO_ADD_SELF_ILLUM": 2, "DETAIL_2_COMBO_CROSSFADE": 5, "DETAIL_2_COMBO_MOD2X": 3, "DETAIL_2_COMBO_MUL": 4, "DETAIL_2_COMBO_OFF": 0, "DETAIL_2_COMBO_UNINITIALIZED": -1}, "type": "uint32"}, "DetailCombo_t": {"alignment": 4, "members": {"DETAIL_COMBO_ADD": 1, "DETAIL_COMBO_ADD_SELF_ILLUM": 2, "DETAIL_COMBO_MOD2X": 3, "DETAIL_COMBO_OFF": 0}, "type": "uint32"}, "EventTypeSelection_t": {"alignment": 4, "members": {"PARTICLE_EVENT_TYPE_MASK_COLLISION": 4, "PARTICLE_EVENT_TYPE_MASK_COLLISION_STOPPED": 16, "PARTICLE_EVENT_TYPE_MASK_FIRST_COLLISION": 8, "PARTICLE_EVENT_TYPE_MASK_KILLED": 2, "PARTICLE_EVENT_TYPE_MASK_KILLED_ON_COLLISION": 32, "PARTICLE_EVENT_TYPE_MASK_NONE": 0, "PARTICLE_EVENT_TYPE_MASK_SPAWNED": 1, "PARTICLE_EVENT_TYPE_MASK_USER_1": 64, "PARTICLE_EVENT_TYPE_MASK_USER_2": 128, "PARTICLE_EVENT_TYPE_MASK_USER_3": 256, "PARTICLE_EVENT_TYPE_MASK_USER_4": 512}, "type": "uint32"}, "HitboxLerpType_t": {"alignment": 4, "members": {"HITBOX_LERP_CONSTANT": 1, "HITBOX_LERP_LIFETIME": 0}, "type": "uint32"}, "InheritableBoolType_t": {"alignment": 4, "members": {"INHERITABLE_BOOL_FALSE": 1, "INHERITABLE_BOOL_INHERIT": 0, "INHERITABLE_BOOL_TRUE": 2}, "type": "uint32"}, "MaterialProxyType_t": {"alignment": 4, "members": {"MATERIAL_PROXY_STATUS_EFFECT": 0, "MATERIAL_PROXY_TINT": 1}, "type": "uint32"}, "MissingParentInheritBehavior_t": {"alignment": 4, "members": {"MISSING_PARENT_DO_NOTHING": -1, "MISSING_PARENT_FIND_NEW": 1, "MISSING_PARENT_KILL": 0, "MISSING_PARENT_SAME_INDEX": 2}, "type": "uint32"}, "ModelHitboxType_t": {"alignment": 4, "members": {"MODEL_HITBOX_TYPE_RAW_BONES": 1, "MODEL_HITBOX_TYPE_RENDERBOUNDS": 2, "MODEL_HITBOX_TYPE_SNAPSHOT": 3, "MODEL_HITBOX_TYPE_STANDARD": 0}, "type": "uint32"}, "PFNoiseModifier_t": {"alignment": 4, "members": {"PF_NOISE_MODIFIER_CLUMPS": 2, "PF_NOISE_MODIFIER_LINES": 1, "PF_NOISE_MODIFIER_NONE": 0, "PF_NOISE_MODIFIER_RINGS": 3}, "type": "uint32"}, "PFNoiseTurbulence_t": {"alignment": 4, "members": {"PF_NOISE_TURB_ALTERNATE": 5, "PF_NOISE_TURB_CONTRAST": 4, "PF_NOISE_TURB_FEEDBACK": 2, "PF_NOISE_TURB_HIGHLIGHT": 1, "PF_NOISE_TURB_LOOPY": 3, "PF_NOISE_TURB_NONE": 0}, "type": "uint32"}, "PFNoiseType_t": {"alignment": 4, "members": {"PF_NOISE_TYPE_CURL": 3, "PF_NOISE_TYPE_PERLIN": 0, "PF_NOISE_TYPE_SIMPLEX": 1, "PF_NOISE_TYPE_WORLEY": 2}, "type": "uint32"}, "PFuncVisualizationType_t": {"alignment": 4, "members": {"PFUNC_VISUALIZATION_BOX": 2, "PFUNC_VISUALIZATION_CYLINDER": 6, "PFUNC_VISUALIZATION_LINE": 5, "PFUNC_VISUALIZATION_PLANE": 4, "PFUNC_VISUALIZATION_RING": 3, "PFUNC_VISUALIZATION_SPHERE_SOLID": 1, "PFUNC_VISUALIZATION_SPHERE_WIREFRAME": 0}, "type": "uint32"}, "ParticleAlphaReferenceType_t": {"alignment": 4, "members": {"PARTICLE_ALPHA_REFERENCE_ALPHA_ALPHA": 0, "PARTICLE_ALPHA_REFERENCE_ALPHA_OPAQUE": 2, "PARTICLE_ALPHA_REFERENCE_OPAQUE_ALPHA": 1, "PARTICLE_ALPHA_REFERENCE_OPAQUE_OPAQUE": 3}, "type": "uint32"}, "ParticleAttrBoxFlags_t": {"alignment": 4, "members": {"PARTICLE_ATTR_BOX_FLAGS_NONE": 0, "PARTICLE_ATTR_BOX_FLAGS_WATER": 1}, "type": "uint32"}, "ParticleCollisionMode_t": {"alignment": 4, "members": {"COLLISION_MODE_DISABLED": -1, "COLLISION_MODE_INITIAL_TRACE_DOWN": 0, "COLLISION_MODE_PER_FRAME_PLANESET": 1, "COLLISION_MODE_PER_PARTICLE_TRACE": 3, "COLLISION_MODE_USE_NEAREST_TRACE": 2}, "type": "uint32"}, "ParticleColorBlendMode_t": {"alignment": 4, "members": {"PARTICLEBLEND_DARKEN": 2, "PARTICLEBLEND_DEFAULT": 0, "PARTICLEBLEND_LIGHTEN": 3, "PARTICLEBLEND_MULTIPLY": 4, "PARTICLEBLEND_OVERLAY": 1}, "type": "uint32"}, "ParticleColorBlendType_t": {"alignment": 4, "members": {"PARTICLE_COLOR_BLEND_ADD": 3, "PARTICLE_COLOR_BLEND_AVERAGE": 10, "PARTICLE_COLOR_BLEND_DIVIDE": 2, "PARTICLE_COLOR_BLEND_LUMINANCE": 12, "PARTICLE_COLOR_BLEND_MAX": 7, "PARTICLE_COLOR_BLEND_MIN": 8, "PARTICLE_COLOR_BLEND_MOD2X": 5, "PARTICLE_COLOR_BLEND_MULTIPLY": 0, "PARTICLE_COLOR_BLEND_MULTIPLY2X": 1, "PARTICLE_COLOR_BLEND_NEGATE": 11, "PARTICLE_COLOR_BLEND_REPLACE": 9, "PARTICLE_COLOR_BLEND_SCREEN": 6, "PARTICLE_COLOR_BLEND_SUBTRACT": 4}, "type": "uint32"}, "ParticleControlPointAxis_t": {"alignment": 4, "members": {"PARTICLE_CP_AXIS_NEGATIVE_X": 3, "PARTICLE_CP_AXIS_NEGATIVE_Y": 4, "PARTICLE_CP_AXIS_NEGATIVE_Z": 5, "PARTICLE_CP_AXIS_X": 0, "PARTICLE_CP_AXIS_Y": 1, "PARTICLE_CP_AXIS_Z": 2}, "type": "uint32"}, "ParticleDepthFeatheringMode_t": {"alignment": 4, "members": {"PARTICLE_DEPTH_FEATHERING_OFF": 0, "PARTICLE_DEPTH_FEATHERING_ON_OPTIONAL": 1, "PARTICLE_DEPTH_FEATHERING_ON_REQUIRED": 2}, "type": "uint32"}, "ParticleDetailLevel_t": {"alignment": 4, "members": {"PARTICLEDETAIL_HIGH": 2, "PARTICLEDETAIL_LOW": 0, "PARTICLEDETAIL_MEDIUM": 1, "PARTICLEDETAIL_ULTRA": 3}, "type": "uint32"}, "ParticleDirectionNoiseType_t": {"alignment": 4, "members": {"PARTICLE_DIR_NOISE_CURL": 1, "PARTICLE_DIR_NOISE_PERLIN": 0, "PARTICLE_DIR_NOISE_WORLEY_BASIC": 2}, "type": "uint32"}, "ParticleEndcapMode_t": {"alignment": 4, "members": {"PARTICLE_ENDCAP_ALWAYS_ON": -1, "PARTICLE_ENDCAP_ENDCAP_OFF": 0, "PARTICLE_ENDCAP_ENDCAP_ON": 1}, "type": "uint32"}, "ParticleFalloffFunction_t": {"alignment": 4, "members": {"PARTICLE_FALLOFF_CONSTANT": 0, "PARTICLE_FALLOFF_EXPONENTIAL": 2, "PARTICLE_FALLOFF_LINEAR": 1}, "type": "uint32"}, "ParticleFloatBiasType_t": {"alignment": 4, "members": {"PF_BIAS_TYPE_COUNT": 3, "PF_BIAS_TYPE_EXPONENTIAL": 2, "PF_BIAS_TYPE_GAIN": 1, "PF_BIAS_TYPE_INVALID": -1, "PF_BIAS_TYPE_STANDARD": 0}, "type": "uint32"}, "ParticleFloatInputMode_t": {"alignment": 4, "members": {"PF_INPUT_MODE_CLAMPED": 0, "PF_INPUT_MODE_COUNT": 2, "PF_INPUT_MODE_INVALID": -1, "PF_INPUT_MODE_LOOPED": 1}, "type": "uint32"}, "ParticleFloatMapType_t": {"alignment": 4, "members": {"PF_MAP_TYPE_COUNT": 7, "PF_MAP_TYPE_CURVE": 4, "PF_MAP_TYPE_DIRECT": 0, "PF_MAP_TYPE_INVALID": -1, "PF_MAP_TYPE_MULT": 1, "PF_MAP_TYPE_NOTCHED": 5, "PF_MAP_TYPE_REMAP": 2, "PF_MAP_TYPE_REMAP_BIASED": 3, "PF_MAP_TYPE_ROUND": 6}, "type": "uint32"}, "ParticleFloatRandomMode_t": {"alignment": 4, "members": {"PF_RANDOM_MODE_CONSTANT": 0, "PF_RANDOM_MODE_COUNT": 2, "PF_RANDOM_MODE_INVALID": -1, "PF_RANDOM_MODE_VARYING": 1}, "type": "uint32"}, "ParticleFloatRoundType_t": {"alignment": 4, "members": {"PF_ROUND_TYPE_CEIL": 2, "PF_ROUND_TYPE_COUNT": 3, "PF_ROUND_TYPE_FLOOR": 1, "PF_ROUND_TYPE_INVALID": -1, "PF_ROUND_TYPE_NEAREST": 0}, "type": "uint32"}, "ParticleFloatType_t": {"alignment": 4, "members": {"PF_TYPE_CLOSEST_CAMERA_DISTANCE": 11, "PF_TYPE_COLLECTION_AGE": 4, "PF_TYPE_CONCURRENT_DEF_COUNT": 10, "PF_TYPE_CONTROL_POINT_CHANGE_AGE": 7, "PF_TYPE_CONTROL_POINT_COMPONENT": 6, "PF_TYPE_CONTROL_POINT_SPEED": 8, "PF_TYPE_COUNT": 22, "PF_TYPE_ENDCAP_AGE": 5, "PF_TYPE_INVALID": -1, "PF_TYPE_LITERAL": 0, "PF_TYPE_NAMED_VALUE": 1, "PF_TYPE_PARTICLE_AGE": 15, "PF_TYPE_PARTICLE_AGE_NORMALIZED": 16, "PF_TYPE_PARTICLE_DETAIL_LEVEL": 9, "PF_TYPE_PARTICLE_FLOAT": 17, "PF_TYPE_PARTICLE_NOISE": 14, "PF_TYPE_PARTICLE_NUMBER": 20, "PF_TYPE_PARTICLE_NUMBER_NORMALIZED": 21, "PF_TYPE_PARTICLE_SPEED": 19, "PF_TYPE_PARTICLE_VECTOR_COMPONENT": 18, "PF_TYPE_RANDOM_BIASED": 3, "PF_TYPE_RANDOM_UNIFORM": 2, "PF_TYPE_RENDERER_CAMERA_DISTANCE": 12, "PF_TYPE_RENDERER_CAMERA_DOT_PRODUCT": 13}, "type": "uint32"}, "ParticleFogType_t": {"alignment": 4, "members": {"PARTICLE_FOG_DISABLED": 2, "PARTICLE_FOG_ENABLED": 1, "PARTICLE_FOG_GAME_DEFAULT": 0}, "type": "uint32"}, "ParticleHitboxBiasType_t": {"alignment": 4, "members": {"PARTICLE_HITBOX_BIAS_ENTITY": 0, "PARTICLE_HITBOX_BIAS_HITBOX": 1}, "type": "uint32"}, "ParticleHitboxDataSelection_t": {"alignment": 4, "members": {"PARTICLE_HITBOX_AVERAGE_SPEED": 0, "PARTICLE_HITBOX_COUNT": 1}, "type": "uint32"}, "ParticleImpulseType_t": {"alignment": 4, "members": {"IMPULSE_TYPE_EXPLOSION": 4, "IMPULSE_TYPE_EXPLOSION_UNDERWATER": 8, "IMPULSE_TYPE_GENERIC": 1, "IMPULSE_TYPE_NONE": 0, "IMPULSE_TYPE_PARTICLE_SYSTEM": 16, "IMPULSE_TYPE_ROPE": 2}, "type": "uint32"}, "ParticleLightBehaviorChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_BEHAVIOR_FOLLOW_DIRECTION": 0, "PARTICLE_LIGHT_BEHAVIOR_ROPE": 1, "PARTICLE_LIGHT_BEHAVIOR_TRAILS": 2}, "type": "uint32"}, "ParticleLightFogLightingMode_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_FOG_LIGHTING_MODE_DYNAMIC": 2, "PARTICLE_LIGHT_FOG_LIGHTING_MODE_DYNAMIC_NOSHADOWS": 4, "PARTICLE_LIGHT_FOG_LIGHTING_MODE_NONE": 0}, "type": "uint32"}, "ParticleLightTypeChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_TYPE_CAPSULE": 3, "PARTICLE_LIGHT_TYPE_FX": 2, "PARTICLE_LIGHT_TYPE_POINT": 0, "PARTICLE_LIGHT_TYPE_SPOT": 1}, "type": "uint32"}, "ParticleLightUnitChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_UNIT_CANDELAS": 0, "PARTICLE_LIGHT_UNIT_LUMENS": 1}, "type": "uint32"}, "ParticleLightingQuality_t": {"alignment": 4, "members": {"PARTICLE_LIGHTING_PER_PARTICLE": 0, "PARTICLE_LIGHTING_PER_PIXEL": -1, "PARTICLE_LIGHTING_PER_VERTEX": 1}, "type": "uint32"}, "ParticleLightnintBranchBehavior_t": {"alignment": 4, "members": {"PARTICLE_LIGHTNING_BRANCH_CURRENT_DIR": 0, "PARTICLE_LIGHTNING_BRANCH_ENDPOINT_DIR": 1}, "type": "uint32"}, "ParticleMassMode_t": {"alignment": 4, "members": {"PARTICLE_MASSMODE_RADIUS_CUBED": 0, "PARTICLE_MASSMODE_RADIUS_SQUARED": 2}, "type": "uint32"}, "ParticleModelType_t": {"alignment": 4, "members": {"PM_TYPE_CONTROL_POINT": 3, "PM_TYPE_COUNT": 4, "PM_TYPE_INVALID": 0, "PM_TYPE_NAMED_VALUE_EHANDLE": 2, "PM_TYPE_NAMED_VALUE_MODEL": 1}, "type": "uint32"}, "ParticleOmni2LightTypeChoiceList_t": {"alignment": 4, "members": {"PARTICLE_OMNI2_LIGHT_TYPE_POINT": 0, "PARTICLE_OMNI2_LIGHT_TYPE_SPHERE": 1}, "type": "uint32"}, "ParticleOrientationChoiceList_t": {"alignment": 4, "members": {"PARTICLE_ORIENTATION_ALIGN_TO_PARTICLE_NORMAL": 3, "PARTICLE_ORIENTATION_FULL_3AXIS_ROTATION": 5, "PARTICLE_ORIENTATION_SCREENALIGN_TO_PARTICLE_NORMAL": 4, "PARTICLE_ORIENTATION_SCREEN_ALIGNED": 0, "PARTICLE_ORIENTATION_SCREEN_Z_ALIGNED": 1, "PARTICLE_ORIENTATION_WORLD_Z_ALIGNED": 2}, "type": "uint32"}, "ParticleOrientationSetMode_t": {"alignment": 4, "members": {"PARTICLE_ORIENTATION_SET_FROM_ROTATIONS": 1, "PARTICLE_ORIENTATION_SET_FROM_VELOCITY": 0}, "type": "uint32"}, "ParticleOutputBlendMode_t": {"alignment": 4, "members": {"PARTICLE_OUTPUT_BLEND_MODE_ADD": 1, "PARTICLE_OUTPUT_BLEND_MODE_ALPHA": 0, "PARTICLE_OUTPUT_BLEND_MODE_BLEND_ADD": 2, "PARTICLE_OUTPUT_BLEND_MODE_HALF_BLEND_ADD": 3, "PARTICLE_OUTPUT_BLEND_MODE_LIGHTEN": 6, "PARTICLE_OUTPUT_BLEND_MODE_MOD2X": 5, "PARTICLE_OUTPUT_BLEND_MODE_NEG_HALF_BLEND_ADD": 4}, "type": "uint32"}, "ParticleParentSetMode_t": {"alignment": 4, "members": {"PARTICLE_SET_PARENT_IMMEDIATE": 1, "PARTICLE_SET_PARENT_NO": 0, "PARTICLE_SET_PARENT_ROOT": 1}, "type": "uint32"}, "ParticlePinDistance_t": {"alignment": 4, "members": {"PARTICLE_PIN_COLLECTION_AGE": 10, "PARTICLE_PIN_DISTANCE_CENTER": 5, "PARTICLE_PIN_DISTANCE_CP": 6, "PARTICLE_PIN_DISTANCE_CP_PAIR_BOTH": 8, "PARTICLE_PIN_DISTANCE_CP_PAIR_EITHER": 7, "PARTICLE_PIN_DISTANCE_FARTHEST": 1, "PARTICLE_PIN_DISTANCE_FIRST": 2, "PARTICLE_PIN_DISTANCE_LAST": 3, "PARTICLE_PIN_DISTANCE_NEIGHBOR": 0, "PARTICLE_PIN_DISTANCE_NONE": -1, "PARTICLE_PIN_FLOAT_VALUE": 11, "PARTICLE_PIN_SPEED": 9}, "type": "uint32"}, "ParticlePostProcessPriorityGroup_t": {"alignment": 4, "members": {"PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_EFFECT": 2, "PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_STATE_HIGH": 4, "PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_STATE_LOW": 3, "PARTICLE_POST_PROCESS_PRIORITY_GLOBAL_UI": 5, "PARTICLE_POST_PROCESS_PRIORITY_LEVEL_OVERRIDE": 1, "PARTICLE_POST_PROCESS_PRIORITY_LEVEL_VOLUME": 0}, "type": "uint32"}, "ParticleReplicationMode_t": {"alignment": 4, "members": {"PARTICLE_REPLICATIONMODE_NONE": 0, "PARTICLE_REPLICATIONMODE_REPLICATE_FOR_EACH_PARENT_PARTICLE": 1}, "type": "uint32"}, "ParticleRotationLockType_t": {"alignment": 4, "members": {"PARTICLE_ROTATION_LOCK_NONE": 0, "PARTICLE_ROTATION_LOCK_NORMAL": 2, "PARTICLE_ROTATION_LOCK_ROTATIONS": 1}, "type": "uint32"}, "ParticleSelection_t": {"alignment": 4, "members": {"PARTICLE_SELECTION_FIRST": 0, "PARTICLE_SELECTION_LAST": 1, "PARTICLE_SELECTION_NUMBER": 2}, "type": "uint32"}, "ParticleSequenceCropOverride_t": {"alignment": 4, "members": {"PARTICLE_SEQUENCE_CROP_OVERRIDE_DEFAULT": -1, "PARTICLE_SEQUENCE_CROP_OVERRIDE_FORCE_OFF": 0, "PARTICLE_SEQUENCE_CROP_OVERRIDE_FORCE_ON": 1}, "type": "uint32"}, "ParticleSetMethod_t": {"alignment": 4, "members": {"PARTICLE_SET_ADD_TO_CURRENT_VALUE": 5, "PARTICLE_SET_ADD_TO_INITIAL_VALUE": 2, "PARTICLE_SET_RAMP_CURRENT_VALUE": 3, "PARTICLE_SET_REPLACE_VALUE": 0, "PARTICLE_SET_SCALE_CURRENT_VALUE": 4, "PARTICLE_SET_SCALE_INITIAL_VALUE": 1}, "type": "uint32"}, "ParticleSortingChoiceList_t": {"alignment": 4, "members": {"PARTICLE_SORTING_CREATION_TIME": 1, "PARTICLE_SORTING_NEAREST": 0}, "type": "uint32"}, "ParticleTextureLayerBlendType_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_BLEND_ADD": 3, "SPRITECARD_TEXTURE_BLEND_AVERAGE": 5, "SPRITECARD_TEXTURE_BLEND_LUMINANCE": 6, "SPRITECARD_TEXTURE_BLEND_MOD2X": 1, "SPRITECARD_TEXTURE_BLEND_MULTIPLY": 0, "SPRITECARD_TEXTURE_BLEND_REPLACE": 2, "SPRITECARD_TEXTURE_BLEND_SUBTRACT": 4}, "type": "uint32"}, "ParticleTopology_t": {"alignment": 4, "members": {"PARTICLE_TOPOLOGY_CUBES": 4, "PARTICLE_TOPOLOGY_LINES": 1, "PARTICLE_TOPOLOGY_POINTS": 0, "PARTICLE_TOPOLOGY_QUADS": 3, "PARTICLE_TOPOLOGY_TRIS": 2}, "type": "uint32"}, "ParticleTraceMissBehavior_t": {"alignment": 4, "members": {"PARTICLE_TRACE_MISS_BEHAVIOR_KILL": 1, "PARTICLE_TRACE_MISS_BEHAVIOR_NONE": 0, "PARTICLE_TRACE_MISS_BEHAVIOR_TRACE_END": 2}, "type": "uint32"}, "ParticleTraceSet_t": {"alignment": 4, "members": {"PARTICLE_TRACE_SET_ALL": 0, "PARTICLE_TRACE_SET_DYNAMIC": 3, "PARTICLE_TRACE_SET_STATIC": 1, "PARTICLE_TRACE_SET_STATIC_AND_KEYFRAMED": 2}, "type": "uint32"}, "ParticleTransformType_t": {"alignment": 4, "members": {"PT_TYPE_CONTROL_POINT": 2, "PT_TYPE_CONTROL_POINT_RANGE": 3, "PT_TYPE_COUNT": 4, "PT_TYPE_INVALID": 0, "PT_TYPE_NAMED_VALUE": 1}, "type": "uint32"}, "ParticleVRHandChoiceList_t": {"alignment": 4, "members": {"PARTICLE_VRHAND_CP": 2, "PARTICLE_VRHAND_CP_OBJECT": 3, "PARTICLE_VRHAND_LEFT": 0, "PARTICLE_VRHAND_RIGHT": 1}, "type": "uint32"}, "ParticleVecType_t": {"alignment": 4, "members": {"PVEC_TYPE_CLOSEST_CAMERA_POSITION": 16, "PVEC_TYPE_COUNT": 17, "PVEC_TYPE_CP_DELTA": 15, "PVEC_TYPE_CP_RELATIVE_DIR": 7, "PVEC_TYPE_CP_RELATIVE_POSITION": 6, "PVEC_TYPE_CP_RELATIVE_RANDOM_DIR": 8, "PVEC_TYPE_CP_VALUE": 5, "PVEC_TYPE_FLOAT_COMPONENTS": 9, "PVEC_TYPE_FLOAT_INTERP_CLAMPED": 10, "PVEC_TYPE_FLOAT_INTERP_GRADIENT": 12, "PVEC_TYPE_FLOAT_INTERP_OPEN": 11, "PVEC_TYPE_INVALID": -1, "PVEC_TYPE_LITERAL": 0, "PVEC_TYPE_LITERAL_COLOR": 1, "PVEC_TYPE_NAMED_VALUE": 2, "PVEC_TYPE_PARTICLE_VECTOR": 3, "PVEC_TYPE_PARTICLE_VELOCITY": 4, "PVEC_TYPE_RANDOM_UNIFORM": 13, "PVEC_TYPE_RANDOM_UNIFORM_OFFSET": 14}, "type": "uint32"}, "PetGroundType_t": {"alignment": 4, "members": {"PET_GROUND_GRID": 1, "PET_GROUND_NONE": 0, "PET_GROUND_PLANE": 2}, "type": "uint32"}, "RenderModelSubModelFieldType_t": {"alignment": 4, "members": {"SUBMODEL_AS_BODYGROUP_SUBMODEL": 0, "SUBMODEL_AS_MESHGROUP_INDEX": 1, "SUBMODEL_AS_MESHGROUP_MASK": 2, "SUBMODEL_IGNORED_USE_MODEL_DEFAULT_MESHGROUP_MASK": 3}, "type": "uint32"}, "ScalarExpressionType_t": {"alignment": 4, "members": {"SCALAR_EXPRESSION_ADD": 0, "SCALAR_EXPRESSION_DIVIDE": 3, "SCALAR_EXPRESSION_INPUT_1": 4, "SCALAR_EXPRESSION_MAX": 6, "SCALAR_EXPRESSION_MIN": 5, "SCALAR_EXPRESSION_MOD": 7, "SCALAR_EXPRESSION_MUL": 2, "SCALAR_EXPRESSION_SUBTRACT": 1, "SCALAR_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}, "SnapshotIndexType_t": {"alignment": 4, "members": {"SNAPSHOT_INDEX_DIRECT": 1, "SNAPSHOT_INDEX_INCREMENT": 0}, "type": "uint32"}, "SpriteCardPerParticleScale_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_PP_SCALE_ANIMATION_FRAME": 2, "SPRITECARD_TEXTURE_PP_SCALE_NEG_RANDOM": 11, "SPRITECARD_TEXTURE_PP_SCALE_NEG_RANDOM_TIME": 13, "SPRITECARD_TEXTURE_PP_SCALE_NONE": 0, "SPRITECARD_TEXTURE_PP_SCALE_PARTICLE_AGE": 1, "SPRITECARD_TEXTURE_PP_SCALE_PARTICLE_ALPHA": 5, "SPRITECARD_TEXTURE_PP_SCALE_PITCH": 9, "SPRITECARD_TEXTURE_PP_SCALE_RANDOM": 10, "SPRITECARD_TEXTURE_PP_SCALE_RANDOM_TIME": 12, "SPRITECARD_TEXTURE_PP_SCALE_ROLL": 7, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_EXTRA_DATA1": 3, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_EXTRA_DATA2": 4, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_RADIUS": 6, "SPRITECARD_TEXTURE_PP_SCALE_YAW": 8}, "type": "uint32"}, "SpriteCardShaderType_t": {"alignment": 4, "members": {"SPRITECARD_SHADER_BASE": 0, "SPRITECARD_SHADER_CUSTOM": 1}, "type": "uint32"}, "SpriteCardTextureChannel_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_CHANNEL_MIX_A": 2, "SPRITECARD_TEXTURE_CHANNEL_MIX_A_RGBALPHA": 7, "SPRITECARD_TEXTURE_CHANNEL_MIX_B": 11, "SPRITECARD_TEXTURE_CHANNEL_MIX_BALPHA": 14, "SPRITECARD_TEXTURE_CHANNEL_MIX_G": 10, "SPRITECARD_TEXTURE_CHANNEL_MIX_GALPHA": 13, "SPRITECARD_TEXTURE_CHANNEL_MIX_R": 9, "SPRITECARD_TEXTURE_CHANNEL_MIX_RALPHA": 12, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB": 0, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGBA": 1, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGBA_RGBALPHA": 6, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_A": 3, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_ALPHAMASK": 4, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_A_RGBALPHA": 8, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_RGBMASK": 5}, "type": "uint32"}, "SpriteCardTextureType_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_1D_COLOR_LOOKUP": 2, "SPRITECARD_TEXTURE_ANIMMOTIONVEC": 6, "SPRITECARD_TEXTURE_DIFFUSE": 0, "SPRITECARD_TEXTURE_NORMALMAP": 5, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_A": 7, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_B": 8, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_C": 9, "SPRITECARD_TEXTURE_UVDISTORTION": 3, "SPRITECARD_TEXTURE_UVDISTORTION_ZOOM": 4, "SPRITECARD_TEXTURE_ZOOM": 1}, "type": "uint32"}, "StandardLightingAttenuationStyle_t": {"alignment": 4, "members": {"LIGHT_STYLE_NEW": 1, "LIGHT_STYLE_OLD": 0}, "type": "uint32"}, "TextureRepetitionMode_t": {"alignment": 4, "members": {"TEXTURE_REPETITION_PARTICLE": 0, "TEXTURE_REPETITION_PATH": 1}, "type": "uint32"}, "VectorExpressionType_t": {"alignment": 4, "members": {"VECTOR_EXPRESSION_ADD": 0, "VECTOR_EXPRESSION_CROSSPRODUCT": 7, "VECTOR_EXPRESSION_DIVIDE": 3, "VECTOR_EXPRESSION_INPUT_1": 4, "VECTOR_EXPRESSION_MAX": 6, "VECTOR_EXPRESSION_MIN": 5, "VECTOR_EXPRESSION_MUL": 2, "VECTOR_EXPRESSION_SUBTRACT": 1, "VECTOR_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}, "VectorFloatExpressionType_t": {"alignment": 4, "members": {"VECTOR_FLOAT_EXPRESSION_DISTANCE": 1, "VECTOR_FLOAT_EXPRESSION_DISTANCESQR": 2, "VECTOR_FLOAT_EXPRESSION_DOTPRODUCT": 0, "VECTOR_FLOAT_EXPRESSION_INPUT1_LENGTH": 3, "VECTOR_FLOAT_EXPRESSION_INPUT1_LENGTHSQR": 4, "VECTOR_FLOAT_EXPRESSION_INPUT1_NOISE": 5, "VECTOR_FLOAT_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}}}}