// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace schemas {
        // Module: libnetworksystem.so
        // Classes count: 1
        // Enums count: 0
        namespace libnetworksystem_so {
            // Parent: None
            // Fields count: 1
            namespace ChangeAccessorFieldPathIndex_t {
                constexpr std::ptrdiff_t m_Value = 0x0; // int32
            }
        }
    }
}
