// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace schemas {
        // Module: libhost.so
        // Classes count: 2
        // Enums count: 0
        namespace libhost_so {
            // Parent: CAnimScriptBase
            // Fields count: 1
            namespace EmptyTestScript {
                constexpr std::ptrdiff_t m_hTest = 0x10; // CAnimScriptParam<float32>
            }
            // Parent: None
            // Fields count: 1
            namespace CAnimScriptBase {
                constexpr std::ptrdiff_t m_bIsValid = 0x8; // bool
            }
        }
    }
}
