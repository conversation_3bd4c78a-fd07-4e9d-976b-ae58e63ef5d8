{"libschemasystem.so": {"classes": {"CExampleSchemaVData_Monomorphic": {"fields": {"m_nExample1": 0, "m_nExample2": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CExampleSchemaVData_PolymorphicBase": {"fields": {"m_nBase": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CExampleSchemaVData_PolymorphicDerivedA": {"fields": {"m_nDerivedA": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CExampleSchemaVData_PolymorphicBase"}, "CExampleSchemaVData_PolymorphicDerivedB": {"fields": {"m_nDerivedB": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CExampleSchemaVData_PolymorphicBase"}, "CSchemaSystemInternalRegistration": {"fields": {"m_CTransform": 256, "m_CUtlBinaryBlock": 296, "m_CUtlString": 320, "m_CUtlSymbol": 328, "m_Color": 224, "m_DegreeEuler": 100, "m_KV3": 368, "m_QAngle": 64, "m_Quaternion": 48, "m_QuaternionStorage": 112, "m_RadianEuler": 88, "m_ResourceTypes": 360, "m_RotationVector": 76, "m_Vector": 8, "m_Vector2D": 0, "m_Vector4D": 228, "m_VectorAligned": 32, "m_matrix3x4_t": 128, "m_matrix3x4a_t": 176, "m_pKeyValues": 288, "m_stringToken": 332, "m_stringTokenWithStorage": 336}, "metadata": [], "parent": null}, "InfoForResourceTypeCResourceManifestInternal": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "ResourceId_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}}, "enums": {"ThreeState_t": {"alignment": 4, "members": {"TRS_FALSE": 0, "TRS_NONE": 2, "TRS_TRUE": 1}, "type": "uint32"}, "fieldtype_t": {"alignment": 1, "members": {"FIELD_AI_SCHEDULE_BITS": 69, "FIELD_AMMO_INDEX": 67, "FIELD_ATTACHMENT_HANDLE": 66, "FIELD_BOOLEAN": 6, "FIELD_CHARACTER": 8, "FIELD_CLASSPTR": 12, "FIELD_CMOTIONTRANSFORM": 64, "FIELD_CMOTIONTRANSFORM_WORLDSPACE": 65, "FIELD_COLOR32": 9, "FIELD_CONDITION_ID": 68, "FIELD_CSTRING": 30, "FIELD_CTRANSFORM": 59, "FIELD_CTRANSFORM_WORLDSPACE": 60, "FIELD_CUSTOM": 11, "FIELD_DIRECTION_VECTOR_WORLDSPACE": 45, "FIELD_EHANDLE": 13, "FIELD_EMBEDDED": 10, "FIELD_ENGINE_TICK": 77, "FIELD_ENGINE_TIME": 76, "FIELD_FLOAT32": 1, "FIELD_FLOAT64": 34, "FIELD_FUNCTION": 19, "FIELD_GLOBALSYMBOL": 79, "FIELD_HMATERIAL": 41, "FIELD_HMODEL": 42, "FIELD_HPARTICLESYSTEMDEFINITION": 56, "FIELD_HPOSTPROCESSING": 61, "FIELD_HRENDERTEXTURE": 55, "FIELD_HSCRIPT": 31, "FIELD_HSCRIPT_LIGHTBINDING": 48, "FIELD_HSCRIPT_NEW_INSTANCE": 36, "FIELD_HVDATA": 73, "FIELD_INPUT": 18, "FIELD_INT16": 7, "FIELD_INT32": 5, "FIELD_INT64": 26, "FIELD_INTERVAL": 23, "FIELD_MATRIX3X4": 62, "FIELD_MATRIX3X4_WORLDSPACE": 22, "FIELD_MODIFIER_HANDLE": 70, "FIELD_NETWORK_ORIGIN_CELL_QUANTIZED_POSITION_VECTOR": 54, "FIELD_NETWORK_ORIGIN_CELL_QUANTIZED_VECTOR": 40, "FIELD_NETWORK_QUANTIZED_FLOAT": 44, "FIELD_NETWORK_QUANTIZED_VECTOR": 43, "FIELD_POSITION_VECTOR": 14, "FIELD_POSITIVEINTEGER_OR_NULL": 35, "FIELD_QANGLE": 39, "FIELD_QANGLE_WORLDSPACE": 46, "FIELD_QUATERNION": 4, "FIELD_QUATERNION_WORLDSPACE": 47, "FIELD_RESOURCE": 28, "FIELD_ROTATION_VECTOR": 71, "FIELD_ROTATION_VECTOR_WORLDSPACE": 72, "FIELD_SCALE32": 74, "FIELD_SHIM": 63, "FIELD_SOUNDNAME": 17, "FIELD_STRING": 2, "FIELD_STRING_AND_TOKEN": 75, "FIELD_TICK": 16, "FIELD_TIME": 15, "FIELD_TYPECOUNT": 80, "FIELD_TYPEUNKNOWN": 29, "FIELD_UINT16": 58, "FIELD_UINT32": 37, "FIELD_UINT64": 33, "FIELD_UINT8": 57, "FIELD_UNUSED": 24, "FIELD_UTLSTRING": 53, "FIELD_UTLSTRINGTOKEN": 38, "FIELD_V8_ARRAY": 51, "FIELD_V8_CALLBACK_INFO": 52, "FIELD_V8_OBJECT": 50, "FIELD_V8_VALUE": 49, "FIELD_VARIANT": 32, "FIELD_VECTOR": 3, "FIELD_VECTOR2D": 25, "FIELD_VECTOR4D": 27, "FIELD_VMATRIX": 20, "FIELD_VMATRIX_WORLDSPACE": 21, "FIELD_VOID": 0, "FIELD_WORLD_GROUP_ID": 78}, "type": "uint8"}}}}