// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-22 01:13:18.559538463 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace interfaces {
        // Module: libanimationsystem.so
        namespace libanimationsystem_so {
            constexpr std::ptrdiff_t AnimationSystemUtils_001 = 0x1B4F70;
            constexpr std::ptrdiff_t AnimationSystem_001 = 0x1B4D60;
        }
        // Module: libclient.so
        namespace libclient_so {
            constexpr std::ptrdiff_t ClientToolsInfo_001 = 0xE07340;
            constexpr std::ptrdiff_t EmptyWorldService001_Client = 0xACDC90;
            constexpr std::ptrdiff_t GameClientExports001 = 0xE06F10;
            constexpr std::ptrdiff_t LegacyGameUI001 = 0xFDF680;
            constexpr std::ptrdiff_t Source2Client002 = 0xE06F70;
            constexpr std::ptrdiff_t Source2ClientConfig001 = 0xAB8490;
            constexpr std::ptrdiff_t Source2ClientPrediction001 = 0xE96E90;
            constexpr std::ptrdiff_t Source2ClientUI001 = 0xF6CB90;
        }
        // Module: libengine2.so
        namespace libengine2_so {
            constexpr std::ptrdiff_t BenchmarkService001 = 0x2239C0;
            constexpr std::ptrdiff_t BugService001 = 0x21FB80;
            constexpr std::ptrdiff_t ClientServerEngineLoopService_001 = 0x1EEF60;
            constexpr std::ptrdiff_t EngineGameUI001 = 0x3BE430;
            constexpr std::ptrdiff_t EngineServiceMgr001 = 0x1DF0D0;
            constexpr std::ptrdiff_t GameEventSystemClientV001 = 0x1E4480;
            constexpr std::ptrdiff_t GameEventSystemServerV001 = 0x1E4490;
            constexpr std::ptrdiff_t GameResourceServiceClientV001 = 0x225310;
            constexpr std::ptrdiff_t GameResourceServiceServerV001 = 0x225320;
            constexpr std::ptrdiff_t GameUIService_001 = 0x22D550;
            constexpr std::ptrdiff_t HostStateMgr001 = 0x1E9AA0;
            constexpr std::ptrdiff_t INETSUPPORT_001 = 0x3879C0;
            constexpr std::ptrdiff_t InputService_001 = 0x231C40;
            constexpr std::ptrdiff_t KeyValueCache001 = 0x1EC3F0;
            constexpr std::ptrdiff_t MapListService_001 = 0x246C50;
            constexpr std::ptrdiff_t NetworkClientService_001 = 0x269BB0;
            constexpr std::ptrdiff_t NetworkP2PService_001 = 0x27EEF0;
            constexpr std::ptrdiff_t NetworkServerService_001 = 0x24AFC0;
            constexpr std::ptrdiff_t NetworkService_001 = 0x24A6E0;
            constexpr std::ptrdiff_t RenderService_001 = 0x2840D0;
            constexpr std::ptrdiff_t ScreenshotService001 = 0x286C50;
            constexpr std::ptrdiff_t SimpleEngineLoopService_001 = 0x2056D0;
            constexpr std::ptrdiff_t SoundService_001 = 0x28AFE0;
            constexpr std::ptrdiff_t Source2EngineToClient001 = 0x30C5F0;
            constexpr std::ptrdiff_t Source2EngineToClientStringTable001 = 0x2DF0B0;
            constexpr std::ptrdiff_t Source2EngineToServer001 = 0x333290;
            constexpr std::ptrdiff_t Source2EngineToServerStringTable001 = 0x315C80;
            constexpr std::ptrdiff_t SplitScreenService_001 = 0x292920;
            constexpr std::ptrdiff_t StatsService_001 = 0x2962E0;
            constexpr std::ptrdiff_t ToolService_001 = 0x29AF40;
            constexpr std::ptrdiff_t VENGINE_GAMEUIFUNCS_VERSION005 = 0x3BDE80;
            constexpr std::ptrdiff_t VProfService_001 = 0x29C7C0;
        }
        // Module: libfilesystem_stdio.so
        namespace libfilesystem_stdio_so {
            constexpr std::ptrdiff_t VAsyncFileSystem2_001 = 0x7DB90;
            constexpr std::ptrdiff_t VFileSystem017 = 0x7DB80;
        }
        // Module: libhost.so
        namespace libhost_so {
            constexpr std::ptrdiff_t DebugDrawQueueManager001 = 0xC5180;
            constexpr std::ptrdiff_t GameModelInfo001 = 0xC0190;
            constexpr std::ptrdiff_t GameSystem2HostHook = 0xC08C0;
            constexpr std::ptrdiff_t HostUtils001 = 0xC0D40;
            constexpr std::ptrdiff_t PredictionDiffManager001 = 0xC1CC0;
            constexpr std::ptrdiff_t SaveRestoreDataVersion001 = 0xC3CC0;
            constexpr std::ptrdiff_t SinglePlayerSharedMemory001 = 0xC3F80;
            constexpr std::ptrdiff_t Source2Host001 = 0xC46E0;
        }
        // Module: libinputsystem.so
        namespace libinputsystem_so {
            constexpr std::ptrdiff_t InputStackSystemVersion001 = 0x11500;
            constexpr std::ptrdiff_t InputSystemVersion001 = 0x129F0;
        }
        // Module: liblocalize.so
        namespace liblocalize_so {
            constexpr std::ptrdiff_t Localize_001 = 0x1D770;
        }
        // Module: libmatchmaking.so
        namespace libmatchmaking_so {
            constexpr std::ptrdiff_t GameTypes001 = 0xF5D70;
            constexpr std::ptrdiff_t MATCHFRAMEWORK_001 = 0x1CF4C0;
        }
        // Module: libmaterialsystem2.so
        namespace libmaterialsystem2_so {
            constexpr std::ptrdiff_t FontManager_001 = 0x7DDF0;
            constexpr std::ptrdiff_t MaterialUtils_001 = 0x67C30;
            constexpr std::ptrdiff_t PostProcessingSystem_001 = 0x8D2C0;
            constexpr std::ptrdiff_t TextLayout_001 = 0x8A850;
            constexpr std::ptrdiff_t VMaterialSystem2_001 = 0x2C470;
        }
        // Module: libmeshsystem.so
        namespace libmeshsystem_so {
            constexpr std::ptrdiff_t MeshSystem001 = 0x97630;
        }
        // Module: libnetworksystem.so
        namespace libnetworksystem_so {
            constexpr std::ptrdiff_t FlattenedSerializersVersion001 = 0x163840;
            constexpr std::ptrdiff_t NetworkMessagesVersion001 = 0x18C1C0;
            constexpr std::ptrdiff_t NetworkSystemVersion001 = 0x1B5760;
            constexpr std::ptrdiff_t SerializedEntitiesVersion001 = 0x1CF120;
        }
        // Module: libpanorama.so
        namespace libpanorama_so {
            constexpr std::ptrdiff_t PanoramaUIEngine001 = 0x2342D0;
        }
        // Module: libpanorama_text_pango.so
        namespace libpanorama_text_pango_so {
            constexpr std::ptrdiff_t PanoramaTextServices001 = 0xBB8E0;
        }
        // Module: libpanoramauiclient.so
        namespace libpanoramauiclient_so {
            constexpr std::ptrdiff_t PanoramaUIClient001 = 0x10A0D0;
        }
        // Module: libparticles.so
        namespace libparticles_so {
            constexpr std::ptrdiff_t ParticleSystemMgr003 = 0x212AF0;
        }
        // Module: libpulse_system.so
        namespace libpulse_system_so {
            constexpr std::ptrdiff_t IPulseSystem_001 = 0x402F0;
        }
        // Module: librendersystemvulkan.so
        namespace librendersystemvulkan_so {
            constexpr std::ptrdiff_t RenderDeviceMgr001 = 0x14E840;
            constexpr std::ptrdiff_t RenderUtils_001 = 0xCD990;
        }
        // Module: libresourcesystem.so
        namespace libresourcesystem_so {
            constexpr std::ptrdiff_t ResourceSystem013 = 0x30F40;
        }
        // Module: libscenefilecache.so
        namespace libscenefilecache_so {
            constexpr std::ptrdiff_t ResponseRulesCache001 = 0x89040;
            constexpr std::ptrdiff_t SceneFileCache002 = 0x85BA0;
        }
        // Module: libscenesystem.so
        namespace libscenesystem_so {
            constexpr std::ptrdiff_t RenderingPipelines_001 = 0x142660;
            constexpr std::ptrdiff_t SceneSystem_002 = 0x186F90;
            constexpr std::ptrdiff_t SceneUtils_001 = 0x206810;
        }
        // Module: libschemasystem.so
        namespace libschemasystem_so {
            constexpr std::ptrdiff_t SchemaSystem_001 = 0x20FC0;
        }
        // Module: libserver.so
        namespace libserver_so {
            constexpr std::ptrdiff_t EmptyWorldService001_Server = 0xBD2840;
            constexpr std::ptrdiff_t EntitySubclassUtilsV001 = 0x8B9640;
            constexpr std::ptrdiff_t NavGameTest001 = 0x1118F10;
            constexpr std::ptrdiff_t ServerToolsInfo_001 = 0xEE1440;
            constexpr std::ptrdiff_t Source2GameClients001 = 0xEE13D0;
            constexpr std::ptrdiff_t Source2GameDirector001 = 0x709580;
            constexpr std::ptrdiff_t Source2GameEntities001 = 0xEE13C0;
            constexpr std::ptrdiff_t Source2Server001 = 0xEE10C0;
            constexpr std::ptrdiff_t Source2ServerConfig001 = 0xB6EA60;
            constexpr std::ptrdiff_t customnavsystem001 = 0x83C060;
        }
        // Module: libsoundsystem.so
        namespace libsoundsystem_so {
            constexpr std::ptrdiff_t SoundOpSystem001 = 0x176F30;
            constexpr std::ptrdiff_t SoundOpSystemEdit001 = 0xB6E30;
            constexpr std::ptrdiff_t SoundSystem001 = 0x1E62D0;
            constexpr std::ptrdiff_t VMixEditTool001 = 0x21A190;
        }
        // Module: libsteamaudio.so
        namespace libsteamaudio_so {
            constexpr std::ptrdiff_t SteamAudio001 = 0x32410;
        }
        // Module: libtier0.so
        namespace libtier0_so {
            constexpr std::ptrdiff_t TestScriptMgr001 = 0x1B9C40;
            constexpr std::ptrdiff_t VEngineCvar007 = 0xF6200;
            constexpr std::ptrdiff_t VProcessUtils002 = 0x1AD430;
            constexpr std::ptrdiff_t VStringTokenSystem001 = 0x1DFD50;
        }
        // Module: libv8system.so
        namespace libv8system_so {
            constexpr std::ptrdiff_t Source2V8System001 = 0x1B450;
        }
        // Module: libvphysics2.so
        namespace libvphysics2_so {
            constexpr std::ptrdiff_t VPhysics2_Handle_Interface_001 = 0xC92A0;
            constexpr std::ptrdiff_t VPhysics2_Interface_001 = 0xC8E90;
        }
        // Module: libvscript.so
        namespace libvscript_so {
            constexpr std::ptrdiff_t VScriptManager010 = 0x25DA0;
        }
        // Module: libworldrenderer.so
        namespace libworldrenderer_so {
            constexpr std::ptrdiff_t WorldRendererMgr001 = 0xAED00;
        }
        // Module: steamclient.so
        namespace steamclient_so {
            constexpr std::ptrdiff_t CLIENTENGINE_INTERFACE_VERSION005 = 0x1455CA0;
            constexpr std::ptrdiff_t IVALIDATE001 = 0x1451AC0;
            constexpr std::ptrdiff_t SteamClient006 = 0x111AED0;
            constexpr std::ptrdiff_t SteamClient007 = 0x111AEE0;
            constexpr std::ptrdiff_t SteamClient008 = 0x111AEF0;
            constexpr std::ptrdiff_t SteamClient009 = 0x111AF00;
            constexpr std::ptrdiff_t SteamClient010 = 0x111AF10;
            constexpr std::ptrdiff_t SteamClient011 = 0x111AF20;
            constexpr std::ptrdiff_t SteamClient012 = 0x111AF30;
            constexpr std::ptrdiff_t SteamClient013 = 0x111AF40;
            constexpr std::ptrdiff_t SteamClient014 = 0x111AF50;
            constexpr std::ptrdiff_t SteamClient015 = 0x111AF60;
            constexpr std::ptrdiff_t SteamClient016 = 0x111AF70;
            constexpr std::ptrdiff_t SteamClient017 = 0x111AF80;
            constexpr std::ptrdiff_t SteamClient018 = 0x111AF90;
            constexpr std::ptrdiff_t SteamClient019 = 0x111AFA0;
            constexpr std::ptrdiff_t SteamClient020 = 0x111AFB0;
            constexpr std::ptrdiff_t SteamClient021 = 0x111AFC0;
            constexpr std::ptrdiff_t SteamClient022 = 0x111AFD0;
            constexpr std::ptrdiff_t p2pvoice002 = 0x1C9B1B0;
            constexpr std::ptrdiff_t p2pvoicesingleton002 = 0x1C93900;
        }
    }
}
