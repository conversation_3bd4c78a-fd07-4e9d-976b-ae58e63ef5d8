[package]
name = "cs2-external-learning"
version = "0.1.0"
edition = "2024"

[dependencies]
# Memory reading and process interaction
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_System_Diagnostics_Debug",
    "Win32_System_Memory",
    "Win32_System_Threading",
    "Win32_System_ProcessStatus",
    "Win32_System_Diagnostics_ToolHelp",
    "Win32_Security",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Graphics_Gdi"
] }

# GUI and overlay
eframe = "0.28"
egui = "0.28"
sdl2 = "0.37"
x11 = { version = "2.21", features = ["xlib", "xrandr"] }

# Math and utilities
nalgebra = "0.33"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
