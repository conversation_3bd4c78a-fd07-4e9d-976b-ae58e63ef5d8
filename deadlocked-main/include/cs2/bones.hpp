#pragma once

#include <vector>

enum class Bones {
    <PERSON><PERSON>vis = 0,
    Spine1 = 2,
    <PERSON><PERSON><PERSON> = 4,
    <PERSON> = 5,
    <PERSON> = 6,
    <PERSON><PERSON><PERSON><PERSON> = 8,
    <PERSON><PERSON><PERSON><PERSON> = 9,
    LeftHand = 10,
    <PERSON><PERSON>houlder = 13,
    <PERSON><PERSON><PERSON><PERSON> = 14,
    RightHand = 15,
    LeftHip = 22,
    <PERSON><PERSON><PERSON> = 23,
    <PERSON>Foot = 24,
    <PERSON>Hip = 25,
    <PERSON><PERSON><PERSON> = 26,
    <PERSON>Foot = 27
};

const std::vector<Bones> all_bones = {
    Bones::<PERSON><PERSON><PERSON>,       Bones::Spine1,    Bones::Spine2,   Bones::Neck,          Bones::Head,
    Bones::Left<PERSON>houlder, Bones::<PERSON><PERSON><PERSON><PERSON>, Bones::LeftHand, Bones::<PERSON><PERSON>houlder, Bones::<PERSON><PERSON><PERSON><PERSON>,
    Bones::RightHand,    Bones::LeftHip,   Bones::<PERSON><PERSON>nee, Bones::LeftFoot,      Bones::RightHip,
    Bones::Right<PERSON>nee,    Bones::RightFoot};

const std::vector<std::pair<Bones, Bones>> bone_connections = {
    {Bones::Pelvis, Bones::Spine1},
    {Bones::Spine1, Bones::Spine2},
    {Bones::Spin<PERSON>2, Bones::Neck},
    {Bones::Neck, Bones::Head},
    {Bones::Neck, Bones::<PERSON><PERSON><PERSON>lder},
    {Bones::<PERSON><PERSON><PERSON><PERSON>, Bones::<PERSON><PERSON><PERSON><PERSON>},
    {Bones::<PERSON><PERSON><PERSON><PERSON>, Bones::LeftHand},
    {Bones::Neck, Bones::RightShoulder},
    {Bones::RightShoulder, Bones::RightElbow},
    {Bones::RightElbow, Bones::RightHand},
    {Bones::Pelvis, Bones::LeftHip},
    {Bones::LeftHip, Bones::LeftKnee},
    {Bones::LeftKnee, Bones::LeftFoot},
    {Bones::Pelvis, Bones::RightHip},
    {Bones::RightHip, Bones::RightKnee},
    {Bones::RightKnee, Bones::RightFoot},
};
