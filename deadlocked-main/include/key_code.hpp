#pragma once

#include <map>

enum KeyCode {
    Key<PERSON><PERSON>,
    Key0,
    Key1,
    Key2,
    Key3,
    Key4,
    Key5,
    Key6,
    Key7,
    Key8,
    Key9,
    Key<PERSON>,
    KeyB,
    KeyC,
    KeyD,
    KeyE,
    KeyF,
    KeyG,
    KeyH,
    KeyI,
    KeyJ,
    KeyK,
    KeyL,
    KeyM,
    KeyN,
    KeyO,
    KeyP,
    KeyQ,
    KeyR,
    KeyS,
    KeyT,
    KeyU,
    KeyV,
    KeyW,
    KeyX,
    KeyY,
    KeyZ,
    /*KeyPAD_0,
    KeyPAD_1,
    KeyPAD_2,
    KeyPAD_3,
    KeyPAD_4,
    KeyPAD_5,
    KeyPAD_6,
    KeyPAD_7,
    KeyPAD_8,
    KeyPAD_9,
    KeyPAD_DIVIDE,
    KeyPAD_MULTIPLY,
    KeyPAD_MINUS,
    KeyPAD_PLUS,
    KeyPAD_ENTER,
    KeyPAD_DECIMAL,
    KeyLBRACKET,
    KeyRBRACKET,
    KeySEMICOLON,
    KeyAPOSTROPHE,
    KeyBACKQUOTE,
    KeyCOMMA,
    KeyPERIOD,
    KeySLASH,
    KeyBACKSLASH,
    KeyMINUS,
    KeyEQUAL,
    <PERSON>ENTER,*/
    KeySpace = 66,
    <PERSON>B<PERSON><PERSON>,
    KeyTab,
    KeyCapslock,
    /*KeyNUMLOCK,
    KeyESCAPE,
    KeySCROLLLOCK,
    KeyINSERT,
    KeyDELETE,
    KeyHOME,
    KeyEND,
    KeyPAGEUP,
    KeyPAGEDOWN,
    KeyBREAK,*/
    KeyLeftShift = 80,
    // KeyRSHIFT,
    KeyLeftAlt = 82,
    // KeyRALT,
    KeyLeftControl = 84,
    /*KeyRCONTROL,
    KeyLWIN,
    KeyRWIN,
    KeyAPP,
    KeyUP,
    KeyLEFT,
    KeyDOWN,
    KeyRIGHT,
    KeyF1,
    KeyF2,
    KeyF3,
    KeyF4,
    KeyF5,
    KeyF6,
    KeyF7,
    KeyF8,
    KeyF9,
    KeyF10,
    KeyF11,
    KeyF12,
    KeyCAPSLOCKTOGGLE,
    KeyNUMLOCKTOGGLE,
    KeySCROLLLOCKTOGGLE,*/

    MouseLeft = 317,
    MouseRight,
    MouseMiddle,
    Mouse4,
    Mouse5,
    // MouseWheelUp,
    // MouseWheelDown,
};

struct EnumEntry {
    KeyCode key;
    const char *name;
};

const std::map<KeyCode, const char *> key_code_names = {
    {KeyCode::KeyNone, "None"},
    {KeyCode::Key0, "0"},
    {KeyCode::Key1, "1"},
    {KeyCode::Key2, "2"},
    {KeyCode::Key3, "3"},
    {KeyCode::Key4, "4"},
    {KeyCode::Key5, "5"},
    {KeyCode::Key6, "6"},
    {KeyCode::Key7, "7"},
    {KeyCode::Key8, "8"},
    {KeyCode::Key9, "9"},
    {KeyCode::KeyA, "A"},
    {KeyCode::KeyB, "B"},
    {KeyCode::KeyC, "C"},
    {KeyCode::KeyD, "D"},
    {KeyCode::KeyE, "E"},
    {KeyCode::KeyF, "F"},
    {KeyCode::KeyG, "G"},
    {KeyCode::KeyH, "H"},
    {KeyCode::KeyI, "I"},
    {KeyCode::KeyJ, "J"},
    {KeyCode::KeyK, "K"},
    {KeyCode::KeyL, "L"},
    {KeyCode::KeyM, "M"},
    {KeyCode::KeyN, "N"},
    {KeyCode::KeyO, "O"},
    {KeyCode::KeyP, "P"},
    {KeyCode::KeyQ, "Q"},
    {KeyCode::KeyR, "R"},
    {KeyCode::KeyS, "S"},
    {KeyCode::KeyT, "T"},
    {KeyCode::KeyU, "U"},
    {KeyCode::KeyV, "V"},
    {KeyCode::KeyW, "W"},
    {KeyCode::KeyX, "X"},
    {KeyCode::KeyY, "Y"},
    {KeyCode::KeyZ, "Z"},
    {KeyCode::KeySpace, "Space"},
    {KeyCode::KeyBackspace, "Backspace"},
    {KeyCode::KeyTab, "Tab"},
    {KeyCode::KeyCapslock, "Capslock"},
    {KeyCode::KeyLeftShift, "Left Shift"},
    {KeyCode::KeyLeftAlt, "Left Alt"},
    {KeyCode::KeyLeftControl, "Left Control"},
    {KeyCode::MouseLeft, "Mouse Left"},
    {KeyCode::MouseRight, "Mouse Right"},
    {KeyCode::MouseMiddle, "Mouse Middle"},
    {KeyCode::Mouse4, "Mouse 4"},
    {KeyCode::Mouse5, "Mouse 5"}};
