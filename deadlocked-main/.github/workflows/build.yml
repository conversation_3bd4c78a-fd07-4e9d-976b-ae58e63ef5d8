name: Build

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Enable universe repository
        run: sudo add-apt-repository universe

      - name: Update apt and install SDL dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libx11-dev libxext-dev libxrandr-dev libxcursor-dev libxi-dev libxinerama-dev libwayland-dev libwayland-egl-backend-dev libdbus-1-dev libudev-dev libgles2-mesa-dev libdrm-dev libgbm-dev libasound2-dev meson libpsl-dev

      - name: Grant execution permission on build script
        run: chmod +x build.sh

      - name: Run build script
        run: ./build.sh Release

      - name: Upload build artifact
        uses: actions/upload-artifact@v4
        with:
          name: deadlocked-binary
          path: build/deadlocked
